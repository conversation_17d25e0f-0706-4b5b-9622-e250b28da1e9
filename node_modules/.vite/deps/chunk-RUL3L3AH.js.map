{"version": 3, "sources": ["../../@shikijs/langs/dist/ruby.mjs"], "sourcesContent": ["import html from './html.mjs'\nimport haml from './haml.mjs'\nimport xml from './xml.mjs'\nimport sql from './sql.mjs'\nimport graphql from './graphql.mjs'\nimport css from './css.mjs'\nimport cpp from './cpp.mjs'\nimport c from './c.mjs'\nimport javascript from './javascript.mjs'\nimport shellscript from './shellscript.mjs'\nimport lua from './lua.mjs'\nimport yaml from './yaml.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Ruby\\\",\\\"name\\\":\\\"ruby\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.class.ruby\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.class.ruby\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.ruby\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.separator.inheritance.ruby\\\"},\\\"8\\\":{\\\"name\\\":\\\"entity.other.inherited-class.ruby\\\"},\\\"11\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.ruby\\\"}},\\\"comment\\\":\\\"class Namespace::ClassName < OtherNamespace::OtherClassName\\\",\\\"match\\\":\\\"\\\\b(class)\\\\\\\\s+(([a-zA-Z0-9_]+)((::)[a-zA-Z0-9_]+)*)\\\\\\\\s*((<)\\\\\\\\s*(([a-zA-Z0-9_]+)((::)[a-zA-Z0-9_]+)*))?\\\",\\\"name\\\":\\\"meta.class.ruby\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.module.ruby\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.module.ruby\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.ruby\\\"}},\\\"match\\\":\\\"\\\\b(module)\\\\\\\\s+(([a-zA-Z0-9_]+)((::)[a-zA-Z0-9_]+)*)\\\",\\\"name\\\":\\\"meta.module.ruby\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.class.ruby\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.inheritance.ruby\\\"}},\\\"match\\\":\\\"\\\\b(class)\\\\\\\\s*(<<)\\\\\\\\s*\\\",\\\"name\\\":\\\"meta.class.ruby\\\"},{\\\"comment\\\":\\\"else if is a common mistake carried over from other languages. it works if you put in a second end, but it’s never what you want.\\\",\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\belse(\\\\\\\\s)+if\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.deprecated.ruby\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.ruby\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.assignment.augmented.ruby\\\"}},\\\"comment\\\":\\\"A local variable and/or assignment\\\",\\\"match\\\":\\\"^\\\\\\\\s*([a-z]([A-Za-z0-9_])*)\\\\\\\\s*((&&|\\\\\\\\|\\\\\\\\|)=)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.ruby\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.ruby\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.assignment.augmented.ruby\\\"}},\\\"comment\\\":\\\"A local variable and/or assignment in a condition\\\",\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(case|if|elsif|unless|until|while)\\\\\\\\b\\\\\\\\s*(\\\\\\\\()*?\\\\\\\\s*([a-z]([A-Za-z0-9_])*)\\\\\\\\s*((&&|\\\\\\\\|\\\\\\\\|)=)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.ruby\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.assignment.augmented.ruby\\\"}},\\\"comment\\\":\\\"A local variable operation assignment (+=, -=, *=, /=)\\\",\\\"match\\\":\\\"^\\\\\\\\s*([a-z]([A-Za-z0-9_])*)\\\\\\\\s*((\\\\\\\\+|\\\\\\\\*|-|\\\\\\\\/|%|\\\\\\\\*\\\\\\\\*|&|\\\\\\\\||\\\\\\\\^|<<|>>)=)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.ruby\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.ruby\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.assignment.augmented.ruby\\\"}},\\\"comment\\\":\\\"A local variable operation assignment in a condition\\\",\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(case|if|elsif|unless|until|while)\\\\\\\\b\\\\\\\\s*(\\\\\\\\()*?\\\\\\\\s*([a-z]([A-Za-z0-9_])*)\\\\\\\\s*((\\\\\\\\+|\\\\\\\\*|-|\\\\\\\\/|%|\\\\\\\\*\\\\\\\\*|&|\\\\\\\\||\\\\\\\\^|<<|>>)=)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.ruby\\\"}},\\\"comment\\\":\\\"A local variable assignment\\\",\\\"match\\\":\\\"^\\\\\\\\s*([a-z]([A-Za-z0-9_])*)\\\\\\\\s*=[^=>]\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.ruby\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.ruby\\\"}},\\\"comment\\\":\\\"A local variable assignment in a condition\\\",\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(case|if|elsif|unless|until|while)\\\\\\\\b\\\\\\\\s*(\\\\\\\\()*?\\\\\\\\s*([a-z]([A-Za-z0-9_])*)\\\\\\\\s*=[^=>]\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.constant.hashkey.ruby\\\"}},\\\"comment\\\":\\\"symbols as hash key (1.9 syntax)\\\",\\\"match\\\":\\\"(?>[a-zA-Z_]\\\\\\\\w*(?>[?!])?)(:)(?!:)\\\",\\\"name\\\":\\\"constant.language.symbol.hashkey.ruby\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.constant.ruby\\\"}},\\\"comment\\\":\\\"symbols as hash key (1.8 syntax)\\\",\\\"match\\\":\\\"(?<!:)(:)(?>[a-zA-Z_]\\\\\\\\w*(?>[?!])?)(?=\\\\\\\\s*=>)\\\",\\\"name\\\":\\\"constant.language.symbol.hashkey.ruby\\\"},{\\\"comment\\\":\\\"everything being a reserved word, not a value and needing a 'end' is a..\\\",\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(BEGIN|begin|case|class|else|elsif|END|end|ensure|for|if|in|module|rescue|then|unless|until|when|while)\\\\\\\\b(?![?!])\\\",\\\"name\\\":\\\"keyword.control.ruby\\\"},{\\\"comment\\\":\\\"contextual smart pair support for block parameters\\\",\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\bdo\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.start-block.ruby\\\"},{\\\"comment\\\":\\\"contextual smart pair support\\\",\\\"match\\\":\\\"(?<={)(\\\\\\\\s+)\\\",\\\"name\\\":\\\"meta.syntax.ruby.start-block\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(alias|alias_method|break|next|redo|retry|return|super|undef|yield)\\\\\\\\b(?![?!])|\\\\\\\\bdefined\\\\\\\\?|\\\\\\\\b(block_given|iterator)\\\\\\\\?\\\",\\\"name\\\":\\\"keyword.control.pseudo-method.ruby\\\"},{\\\"match\\\":\\\"\\\\\\\\bnil\\\\\\\\b(?![?!])\\\",\\\"name\\\":\\\"constant.language.nil.ruby\\\"},{\\\"match\\\":\\\"\\\\\\\\b(true|false)\\\\\\\\b(?![?!])\\\",\\\"name\\\":\\\"constant.language.boolean.ruby\\\"},{\\\"match\\\":\\\"\\\\\\\\b(__(FILE|LINE)__)\\\\\\\\b(?![?!])\\\",\\\"name\\\":\\\"variable.language.ruby\\\"},{\\\"match\\\":\\\"\\\\\\\\bself\\\\\\\\b(?![?!])\\\",\\\"name\\\":\\\"variable.language.self.ruby\\\"},{\\\"comment\\\":\\\" everything being a method but having a special function is a..\\\",\\\"match\\\":\\\"\\\\\\\\b(initialize|new|loop|include|extend|prepend|raise|fail|attr_reader|attr_writer|attr_accessor|attr|catch|throw|private|private_class_method|module_function|public|public_class_method|protected|refine|using)\\\\\\\\b(?![?!])\\\",\\\"name\\\":\\\"keyword.other.special-method.ruby\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.|::)(require|require_relative)\\\\\\\\b(?![?!])\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.special-method.ruby\\\"}},\\\"end\\\":\\\"$|(?=#|})\\\",\\\"name\\\":\\\"meta.require.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.ruby\\\"}},\\\"match\\\":\\\"(@)[a-zA-Z_]\\\\\\\\w*\\\",\\\"name\\\":\\\"variable.other.readwrite.instance.ruby\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.ruby\\\"}},\\\"match\\\":\\\"(@@)[a-zA-Z_]\\\\\\\\w*\\\",\\\"name\\\":\\\"variable.other.readwrite.class.ruby\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.ruby\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)[a-zA-Z_]\\\\\\\\w*\\\",\\\"name\\\":\\\"variable.other.readwrite.global.ruby\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.ruby\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)(!|@|&|`|'|\\\\\\\\+|\\\\\\\\d+|~|=|/|\\\\\\\\\\\\\\\\|,|;|\\\\\\\\.|<|>|_|\\\\\\\\*|\\\\\\\\$|\\\\\\\\?|:|\\\\\\\"|-[0adFiIlpv])\\\",\\\"name\\\":\\\"variable.other.readwrite.global.pre-defined.ruby\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(ENV)\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.constant.ruby\\\"}},\\\"end\\\":\\\"]\\\",\\\"name\\\":\\\"meta.environment-variable.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b[A-Z]\\\\\\\\w*(?=((\\\\\\\\.|::)[A-Za-z]|\\\\\\\\[))\\\",\\\"name\\\":\\\"support.class.ruby\\\"},{\\\"match\\\":\\\"\\\\\\\\b((abort|at_exit|autoload|binding|callcc|caller|caller_locations|chomp|chop|eval|exec|exit|fork|format|gets|global_variables|gsub|lambda|load|local_variables|open|p|print|printf|proc|putc|puts|rand|readline|readlines|select|set_trace_func|sleep|spawn|sprintf|srand|sub|syscall|system|test|trace_var|trap|untrace_var|warn)\\\\\\\\b(?![?!])|autoload\\\\\\\\?|exit!)\\\",\\\"name\\\":\\\"support.function.kernel.ruby\\\"},{\\\"match\\\":\\\"\\\\\\\\b[_A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.constant.ruby\\\"},{\\\"begin\\\":\\\"(->)\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.kernel.ruby\\\"}},\\\"comment\\\":\\\"Lambda parameters.\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[&*_a-zA-Z])\\\",\\\"end\\\":\\\"(?=[,)])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#method_parameters\\\"}]},{\\\"include\\\":\\\"#method_parameters\\\"}]},{\\\"begin\\\":\\\"(?=def\\\\\\\\b)(?<=^|\\\\\\\\s)(def)\\\\\\\\s+((?>[a-zA-Z_]\\\\\\\\w*(?>\\\\\\\\.|::))?(?>[a-zA-Z_]\\\\\\\\w*(?>[?!]|=(?!>))?|===?|!=|>[>=]?|<=>|<[<=]?|[%&`/\\\\\\\\|]|\\\\\\\\*\\\\\\\\*?|=?~|[-+]@?|\\\\\\\\[]=?))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.def.ruby\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.ruby\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.ruby\\\"}},\\\"comment\\\":\\\"The method pattern comes from the symbol pattern. See there for an explanation.\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.ruby\\\"}},\\\"name\\\":\\\"meta.function.method.with-arguments.ruby\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[&*_a-zA-Z])\\\",\\\"end\\\":\\\"(?=[,)])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#method_parameters\\\"}]},{\\\"include\\\":\\\"#method_parameters\\\"}]},{\\\"begin\\\":\\\"(?=def\\\\\\\\b)(?<=^|\\\\\\\\s)(def)\\\\\\\\s+((?>[a-zA-Z_]\\\\\\\\w*(?>\\\\\\\\.|::))?(?>[a-zA-Z_]\\\\\\\\w*(?>[?!]|=(?!>))?|===?|!=|>[>=]?|<=>|<[<=]?|[%&`/\\\\\\\\|]|\\\\\\\\*\\\\\\\\*?|=?~|[-+]@?|\\\\\\\\[]=?))[ \\\\\\\\t](?=[ \\\\\\\\t]*[^\\\\\\\\s#;])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.def.ruby\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.ruby\\\"}},\\\"comment\\\":\\\"same as the previous rule, but without parentheses around the arguments\\\",\\\"end\\\":\\\"(?=;)|(?<=[\\\\\\\\w\\\\\\\\])}`'\\\\\\\"!?])(?=\\\\\\\\s*#|\\\\\\\\s*$)\\\",\\\"name\\\":\\\"meta.function.method.with-arguments.ruby\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[&*_a-zA-Z])\\\",\\\"end\\\":\\\"(?=,|;|\\\\\\\\s*#|\\\\\\\\s*$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#method_parameters\\\"}]},{\\\"include\\\":\\\"#method_parameters\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.def.ruby\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.ruby\\\"}},\\\"comment\\\":\\\" the optional name is just to catch the def also without a method-name\\\",\\\"match\\\":\\\"(?=def\\\\\\\\b)(?<=^|\\\\\\\\s)(def)\\\\\\\\b(\\\\\\\\s+((?>[a-zA-Z_]\\\\\\\\w*(?>\\\\\\\\.|::))?(?>[a-zA-Z_]\\\\\\\\w*(?>[?!]|=(?!>))?|===?|!=|>[>=]?|<=>|<[<=]?|[%&`/\\\\\\\\|]|\\\\\\\\*\\\\\\\\*?|=?~|[-+]@?|\\\\\\\\[]=?)))?\\\",\\\"name\\\":\\\"meta.function.method.without-arguments.ruby\\\"},{\\\"match\\\":\\\"\\\\\\\\b([\\\\\\\\d](?>_?\\\\\\\\d)*(\\\\\\\\.(?![^[:space:][:digit:]])(?>_?\\\\\\\\d)*)?([eE][-+]?\\\\\\\\d(?>_?\\\\\\\\d)*)?|0(?:[xX]\\\\\\\\h(?>_?\\\\\\\\h)*|[oO]?[0-7](?>_?[0-7])*|[bB][01](?>_?[01])*|[dD]\\\\\\\\d(?>_?\\\\\\\\d)*))\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.ruby\\\"},{\\\"begin\\\":\\\":'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.symbol.begin.ruby\\\"}},\\\"comment\\\":\\\"symbol literal with '' delimiter\\\",\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.symbol.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\['\\\\\\\\\\\\\\\\]\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"}]},{\\\"begin\\\":\\\":\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.symbol.begin.ruby\\\"}},\\\"comment\\\":\\\"symbol literal with \\\\\\\"\\\\\\\" delimiter\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.symbol.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]},{\\\"comment\\\":\\\"Needs higher precedence than regular expressions.\\\",\\\"match\\\":\\\"(?<!\\\\\\\\()/=\\\",\\\"name\\\":\\\"keyword.operator.assignment.augmented.ruby\\\"},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"comment\\\":\\\"string literal with '' delimiter\\\",\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.single.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\'|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"comment\\\":\\\"string literal with interpolation and \\\\\\\"\\\\\\\" delimiter\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.double.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]},{\\\"begin\\\":\\\"(?<!\\\\\\\\.)`\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"comment\\\":\\\"execute string (allows for interpolation)\\\",\\\"end\\\":\\\"`\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]},{\\\"begin\\\":\\\"(?<![\\\\\\\\w)])((/))(?![?*+])(?=(?:\\\\\\\\\\\\\\\\/|[^/])*+/[eimnosux]*\\\\\\\\s*(?:[)\\\\\\\\]}#.,?:]|\\\\\\\\|\\\\\\\\||&&|<=>|=>|==|=~|!~|!=|;|$|if|else|elsif|then|do|end|unless|while|until|or|and)|$)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.regexp.interpolated.ruby\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.regexp.ruby\\\"}},\\\"comment\\\":\\\"regular expression literal with interpolation\\\",\\\"contentName\\\":\\\"string.regexp.interpolated.ruby\\\",\\\"end\\\":\\\"((/[eimnosux]*))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regex_sub\\\"}]},{\\\"begin\\\":\\\"%r{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.regexp.begin.ruby\\\"}},\\\"end\\\":\\\"}[eimnosux]*\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.regexp.end.ruby\\\"}},\\\"name\\\":\\\"string.regexp.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regex_sub\\\"},{\\\"include\\\":\\\"#nest_curly_r\\\"}]},{\\\"begin\\\":\\\"%r\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.regexp.begin.ruby\\\"}},\\\"end\\\":\\\"][eimnosux]*\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.regexp.end.ruby\\\"}},\\\"name\\\":\\\"string.regexp.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regex_sub\\\"},{\\\"include\\\":\\\"#nest_brackets_r\\\"}]},{\\\"begin\\\":\\\"%r\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.regexp.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\)[eimnosux]*\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.regexp.end.ruby\\\"}},\\\"name\\\":\\\"string.regexp.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regex_sub\\\"},{\\\"include\\\":\\\"#nest_parens_r\\\"}]},{\\\"begin\\\":\\\"%r<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.regexp.begin.ruby\\\"}},\\\"end\\\":\\\">[eimnosux]*\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.regexp.end.ruby\\\"}},\\\"name\\\":\\\"string.regexp.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regex_sub\\\"},{\\\"include\\\":\\\"#nest_ltgt_r\\\"}]},{\\\"begin\\\":\\\"%r([^\\\\\\\\w])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.regexp.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\1[eimnosux]*\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.regexp.end.ruby\\\"}},\\\"name\\\":\\\"string.regexp.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regex_sub\\\"}]},{\\\"begin\\\":\\\"%I\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_brackets_i\\\"}]},{\\\"begin\\\":\\\"%I\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_parens_i\\\"}]},{\\\"begin\\\":\\\"%I<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_ltgt_i\\\"}]},{\\\"begin\\\":\\\"%I{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_curly_i\\\"}]},{\\\"begin\\\":\\\"%I([^\\\\\\\\w])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]},{\\\"begin\\\":\\\"%i\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_brackets\\\"}]},{\\\"begin\\\":\\\"%i\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\)|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_parens\\\"}]},{\\\"begin\\\":\\\"%i<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\>|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_ltgt\\\"}]},{\\\"begin\\\":\\\"%i{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\}|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_curly\\\"}]},{\\\"begin\\\":\\\"%i([^\\\\\\\\w])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.ruby\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"Cant be named because its not necessarily an escape.\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\"}]},{\\\"begin\\\":\\\"%W\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_brackets_i\\\"}]},{\\\"begin\\\":\\\"%W\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_parens_i\\\"}]},{\\\"begin\\\":\\\"%W<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_ltgt_i\\\"}]},{\\\"begin\\\":\\\"%W{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_curly_i\\\"}]},{\\\"begin\\\":\\\"%W([^\\\\\\\\w])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]},{\\\"begin\\\":\\\"%w\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_brackets\\\"}]},{\\\"begin\\\":\\\"%w\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\)|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_parens\\\"}]},{\\\"begin\\\":\\\"%w<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\>|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_ltgt\\\"}]},{\\\"begin\\\":\\\"%w{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\}|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_curly\\\"}]},{\\\"begin\\\":\\\"%w([^\\\\\\\\w])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.ruby\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"Cant be named because its not necessarily an escape.\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\"}]},{\\\"begin\\\":\\\"%[Qx]?\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_parens_i\\\"}]},{\\\"begin\\\":\\\"%[Qx]?\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_brackets_i\\\"}]},{\\\"begin\\\":\\\"%[Qx]?{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_curly_i\\\"}]},{\\\"begin\\\":\\\"%[Qx]?<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_ltgt_i\\\"}]},{\\\"begin\\\":\\\"%[Qx]([^\\\\\\\\w])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]},{\\\"begin\\\":\\\"%([^\\\\\\\\w\\\\\\\\s=])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.interpolated.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]},{\\\"begin\\\":\\\"%q\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\)|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_parens\\\"}]},{\\\"begin\\\":\\\"%q<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\>|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_ltgt\\\"}]},{\\\"begin\\\":\\\"%q\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_brackets\\\"}]},{\\\"begin\\\":\\\"%q{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\}|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_curly\\\"}]},{\\\"begin\\\":\\\"%q([^\\\\\\\\w])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.quoted.other.ruby\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"Cant be named because its not necessarily an escape.\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\"}]},{\\\"begin\\\":\\\"%s\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.symbol.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.symbol.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\)|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_parens\\\"}]},{\\\"begin\\\":\\\"%s<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.symbol.begin.ruby\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.symbol.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\>|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_ltgt\\\"}]},{\\\"begin\\\":\\\"%s\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.symbol.begin.ruby\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.symbol.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_brackets\\\"}]},{\\\"begin\\\":\\\"%s{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.symbol.begin.ruby\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.symbol.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\}|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},{\\\"include\\\":\\\"#nest_curly\\\"}]},{\\\"begin\\\":\\\"%s([^\\\\\\\\w])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.symbol.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.symbol.end.ruby\\\"}},\\\"name\\\":\\\"constant.language.symbol.ruby\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"Cant be named because its not necessarily an escape.\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.constant.ruby\\\"}},\\\"comment\\\":\\\"symbols\\\",\\\"match\\\":\\\"(?<!:)(:)(?>[$a-zA-Z_]\\\\\\\\w*(?>[?!]|=(?![>=]))?|===?|<=>|>[>=]?|<[<=]?|[%&`/\\\\\\\\|]|\\\\\\\\*\\\\\\\\*?|=?~|[-+]@?|\\\\\\\\[]=?|@@?[a-zA-Z_]\\\\\\\\w*)\\\",\\\"name\\\":\\\"constant.language.symbol.ruby\\\"},{\\\"begin\\\":\\\"^=begin\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.ruby\\\"}},\\\"comment\\\":\\\"multiline comments\\\",\\\"end\\\":\\\"^=end\\\",\\\"name\\\":\\\"comment.block.documentation.ruby\\\"},{\\\"include\\\":\\\"#yard\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.ruby\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.ruby\\\"}]},{\\\"comment\\\":\\\"\\\\n\\\\t\\\\t\\\\tmatches questionmark-letters.\\\\n\\\\n\\\\t\\\\t\\\\texamples (1st alternation = hex):\\\\n\\\\t\\\\t\\\\t?\\\\\\\\x1     ?\\\\\\\\x61\\\\n\\\\n\\\\t\\\\t\\\\texamples (2nd alternation = octal):\\\\n\\\\t\\\\t\\\\t?\\\\\\\\0      ?\\\\\\\\07     ?\\\\\\\\017\\\\n\\\\n\\\\t\\\\t\\\\texamples (3rd alternation = escaped):\\\\n\\\\t\\\\t\\\\t?\\\\\\\\n      ?\\\\\\\\b\\\\n\\\\n\\\\t\\\\t\\\\texamples (4th alternation = meta-ctrl):\\\\n\\\\t\\\\t\\\\t?\\\\\\\\C-a    ?\\\\\\\\M-a    ?\\\\\\\\C-\\\\\\\\M-\\\\\\\\C-\\\\\\\\M-a\\\\n\\\\n\\\\t\\\\t\\\\texamples (4th alternation = normal):\\\\n\\\\t\\\\t\\\\t?a       ?A       ?0 \\\\n\\\\t\\\\t\\\\t?*       ?\\\\\\\"       ?( \\\\n\\\\t\\\\t\\\\t?.       ?#\\\\n\\\\t\\\\t\\\\t\\\\n\\\\t\\\\t\\\\t\\\\n\\\\t\\\\t\\\\tthe negative lookbehind prevents against matching\\\\n\\\\t\\\\t\\\\tp(42.tainted?)\\\\n\\\\t\\\\t\\\\t\\\",\\\"match\\\":\\\"(?<!\\\\\\\\w)\\\\\\\\?(\\\\\\\\\\\\\\\\(x\\\\\\\\h{1,2}(?!\\\\\\\\h)\\\\\\\\b|0[0-7]{0,2}(?![0-7])\\\\\\\\b|[^x0MC])|(\\\\\\\\\\\\\\\\[MC]-)+\\\\\\\\w|[^\\\\\\\\s\\\\\\\\\\\\\\\\])\\\",\\\"name\\\":\\\"constant.numeric.ruby\\\"},{\\\"begin\\\":\\\"^__END__\\\\\\\\n\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"string.unquoted.program-block.ruby\\\"}},\\\"comment\\\":\\\"__END__ marker\\\",\\\"contentName\\\":\\\"text.plain\\\",\\\"end\\\":\\\"(?=not)impossible\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=<?xml|<(?i:html\\\\\\\\b)|!DOCTYPE (?i:html\\\\\\\\b))\\\",\\\"end\\\":\\\"(?=not)impossible\\\",\\\"name\\\":\\\"text.html.embedded.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)HTML)\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded HTML\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.html\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)HTML)\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"text.html\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"text.html.basic\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)HAML)\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded HAML\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.haml\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)HAML)\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"text.haml\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"text.haml\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)XML)\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded XML\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.xml\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)XML)\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"text.xml\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"text.xml\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)SQL)\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded SQL\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.sql\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)SQL)\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"source.sql\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"source.sql\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)(?:GRAPHQL|GQL))\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded GraphQL\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.graphql\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)(?:GRAPHQL|GQL))\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"source.graphql\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"source.graphql\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)CSS)\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded CSS\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.css\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)CSS)\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"source.css\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"source.css\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)CPP)\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded C++\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.cpp\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)CPP)\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"source.cpp\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"source.cpp\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)C)\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded C\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.c\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)C)\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"source.c\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"source.c\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)(?:JS|JAVASCRIPT))\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded Javascript\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.js\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)(?:JS|JAVASCRIPT))\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"source.js\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"source.js\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)JQUERY)\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded jQuery Javascript\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.js.jquery\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)JQUERY)\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"source.js.jquery\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"source.js.jquery\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)(?:SH|SHELL))\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded Shell\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.shell\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)(?:SH|SHELL))\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"source.shell\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"source.shell\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)LUA)\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded Lua\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.lua\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)LUA)\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"source.lua\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"source.lua\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)RUBY)\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded Ruby\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.ruby\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)RUBY)\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"source.ruby\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"source.ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)(?:YAML|YML))\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded YAML\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.yaml\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)(?:YAML|YML))\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"source.yaml\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"source.yaml\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?=(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)SLIM)\\\\\\\\b\\\\\\\\1))\\\",\\\"comment\\\":\\\"Heredoc with embedded Slim\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.block.slim\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?><<[-~]([\\\\\\\"'`]?)((?:[_\\\\\\\\w]+_|)SLIM)\\\\\\\\b\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"contentName\\\":\\\"text.slim\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"text.slim\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]}]},{\\\"begin\\\":\\\"(?>=\\\\\\\\s*<<([\\\\\\\"'`]?)(\\\\\\\\w+)\\\\\\\\1)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"}},\\\"end\\\":\\\"^\\\\\\\\2$\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]},{\\\"begin\\\":\\\"(?>((<<[-~]([\\\\\\\"'`]?)(\\\\\\\\w+)\\\\\\\\3,\\\\\\\\s?)*<<[-~]([\\\\\\\"'`]?)(\\\\\\\\w+)\\\\\\\\5))(.*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.ruby\\\"},\\\"7\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"source.ruby\\\"}]}},\\\"comment\\\":\\\"heredoc with multiple inputs and indented terminator\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\6$\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.ruby\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"}]},{\\\"begin\\\":\\\"(?<={|{\\\\\\\\s|[^A-Za-z0-9_:@$]do|^do|[^A-Za-z0-9_:@$]do\\\\\\\\s|^do\\\\\\\\s)(\\\\\\\\|)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.variable.ruby\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\|)(\\\\\\\\|)(?!\\\\\\\\|)\\\",\\\"name\\\":\\\"meta.block.parameters.ruby\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?![\\\\\\\\s,|(])\\\",\\\"end\\\":\\\"(?=,|\\\\\\\\|\\\\\\\\s*)\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.variable.ruby\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.block.ruby\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.block.unused.ruby variable.other.constant.ruby\\\"}},\\\"match\\\":\\\"\\\\\\\\G([&*]?)([a-zA-Z][\\\\\\\\w_]*)|(_[\\\\\\\\w_]*)\\\"}]},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.variable.ruby\\\"}]},{\\\"match\\\":\\\"=>\\\",\\\"name\\\":\\\"punctuation.separator.key-value\\\"},{\\\"match\\\":\\\"->\\\",\\\"name\\\":\\\"support.function.kernel.ruby\\\"},{\\\"match\\\":\\\"<<=|%=|&{1,2}=|\\\\\\\\*=|\\\\\\\\*\\\\\\\\*=|\\\\\\\\+=|-=|\\\\\\\\^=|\\\\\\\\|{1,2}=|<<\\\",\\\"name\\\":\\\"keyword.operator.assignment.augmented.ruby\\\"},{\\\"match\\\":\\\"<=>|<(?!<|=)|>(?!<|=|>)|<=|>=|===|==|=~|!=|!~|(?<=[ \\\\\\\\t])\\\\\\\\?\\\",\\\"name\\\":\\\"keyword.operator.comparison.ruby\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(and|not|or)\\\\\\\\b(?![?!])\\\",\\\"name\\\":\\\"keyword.operator.logical.ruby\\\"},{\\\"match\\\":\\\"(?<=^|[ \\\\\\\\t!])!|&&|\\\\\\\\|\\\\\\\\||\\\\\\\\^\\\",\\\"name\\\":\\\"keyword.operator.logical.ruby\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logical.ruby\\\"}},\\\"comment\\\":\\\"Safe navigation operator\\\",\\\"match\\\":\\\"(&\\\\\\\\.)\\\\\\\\s*(?![A-Z])\\\"},{\\\"match\\\":\\\"(%|&|\\\\\\\\*\\\\\\\\*|\\\\\\\\*|\\\\\\\\+|-|/)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.ruby\\\"},{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.ruby\\\"},{\\\"match\\\":\\\"\\\\\\\\||~|>>\\\",\\\"name\\\":\\\"keyword.operator.other.ruby\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.separator.statement.ruby\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.object.ruby\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.ruby\\\"}},\\\"comment\\\":\\\"Mark as namespace separator if double colons followed by capital letter\\\",\\\"match\\\":\\\"(::)\\\\\\\\s*(?=[A-Z])\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.method.ruby\\\"}},\\\"comment\\\":\\\"Mark as method separator if double colons not followed by capital letter\\\",\\\"match\\\":\\\"(\\\\\\\\.|::)\\\\\\\\s*(?![A-Z])\\\"},{\\\"comment\\\":\\\"Must come after method and constant separators to prefer double colons\\\",\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.other.ruby\\\"},{\\\"match\\\":\\\"{\\\",\\\"name\\\":\\\"punctuation.section.scope.begin.ruby\\\"},{\\\"match\\\":\\\"}\\\",\\\"name\\\":\\\"punctuation.section.scope.end.ruby\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"},{\\\"match\\\":\\\"]\\\",\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"},{\\\"match\\\":\\\"\\\\\\\\(|\\\\\\\\)\\\",\\\"name\\\":\\\"punctuation.section.function.ruby\\\"},{\\\"begin\\\":\\\"(?<=[^\\\\\\\\.]\\\\\\\\.|::)(?=[a-zA-Z][a-zA-Z0-9_!?]*[^a-zA-Z0-9_!?])\\\",\\\"end\\\":\\\"(?<=[a-zA-Z0-9_!?])(?=[^a-zA-Z0-9_!?])\\\",\\\"name\\\":\\\"meta.function-call.ruby\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"([a-zA-Z][a-zA-Z0-9_!?]*)(?=[^a-zA-Z0-9_!?])\\\",\\\"name\\\":\\\"entity.name.function.ruby\\\"}]},{\\\"begin\\\":\\\"([a-zA-Z]\\\\\\\\w*[!?]?)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.ruby\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.ruby\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.function.ruby\\\"}},\\\"name\\\":\\\"meta.function-call.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}],\\\"repository\\\":{\\\"escaped_char\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:[0-7]{1,3}|x[\\\\\\\\da-fA-F]{1,2}|.)\\\",\\\"name\\\":\\\"constant.character.escape.ruby\\\"},\\\"heredoc\\\":{\\\"begin\\\":\\\"^<<[-~]?\\\\\\\\w+\\\",\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"interpolated_ruby\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"#{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.begin.ruby\\\"}},\\\"contentName\\\":\\\"source.ruby\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.end.ruby\\\"}},\\\"name\\\":\\\"meta.embedded.line.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#nest_curly_and_self\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.ruby\\\"}},\\\"match\\\":\\\"(#@)[a-zA-Z_]\\\\\\\\w*\\\",\\\"name\\\":\\\"variable.other.readwrite.instance.ruby\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.ruby\\\"}},\\\"match\\\":\\\"(#@@)[a-zA-Z_]\\\\\\\\w*\\\",\\\"name\\\":\\\"variable.other.readwrite.class.ruby\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.ruby\\\"}},\\\"match\\\":\\\"(#\\\\\\\\$)[a-zA-Z_]\\\\\\\\w*\\\",\\\"name\\\":\\\"variable.other.readwrite.global.ruby\\\"}]},\\\"method_parameters\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#parens\\\"},{\\\"include\\\":\\\"#braces\\\"},{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"#params\\\"},{\\\"include\\\":\\\"$self\\\"}],\\\"repository\\\":{\\\"braces\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.end.ruby\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parens\\\"},{\\\"include\\\":\\\"#braces\\\"},{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"brackets\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.array.end.ruby\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parens\\\"},{\\\"include\\\":\\\"#braces\\\"},{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"params\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.variable.ruby\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.other.symbol.hashkey.parameter.function.ruby\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.constant.ruby\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.parameter.function.ruby\\\"}},\\\"match\\\":\\\"\\\\\\\\G(&|\\\\\\\\*\\\\\\\\*?)?(?:([_a-zA-Z]\\\\\\\\w*[?!]?(:))|([_a-zA-Z]\\\\\\\\w*))\\\"},\\\"parens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.ruby\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parens\\\"},{\\\"include\\\":\\\"#braces\\\"},{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"$self\\\"}]}}},\\\"nest_brackets\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.ruby\\\"}},\\\"end\\\":\\\"]\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#nest_brackets\\\"}]},\\\"nest_brackets_i\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.ruby\\\"}},\\\"end\\\":\\\"]\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_brackets_i\\\"}]},\\\"nest_brackets_r\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.ruby\\\"}},\\\"end\\\":\\\"]\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regex_sub\\\"},{\\\"include\\\":\\\"#nest_brackets_r\\\"}]},\\\"nest_curly\\\":{\\\"begin\\\":\\\"{\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.ruby\\\"}},\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#nest_curly\\\"}]},\\\"nest_curly_and_self\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"{\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.ruby\\\"}},\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#nest_curly_and_self\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},\\\"nest_curly_i\\\":{\\\"begin\\\":\\\"{\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.ruby\\\"}},\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_curly_i\\\"}]},\\\"nest_curly_r\\\":{\\\"begin\\\":\\\"{\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.ruby\\\"}},\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regex_sub\\\"},{\\\"include\\\":\\\"#nest_curly_r\\\"}]},\\\"nest_ltgt\\\":{\\\"begin\\\":\\\"<\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.ruby\\\"}},\\\"end\\\":\\\">\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#nest_ltgt\\\"}]},\\\"nest_ltgt_i\\\":{\\\"begin\\\":\\\"<\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.ruby\\\"}},\\\"end\\\":\\\">\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_ltgt_i\\\"}]},\\\"nest_ltgt_r\\\":{\\\"begin\\\":\\\"<\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.ruby\\\"}},\\\"end\\\":\\\">\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regex_sub\\\"},{\\\"include\\\":\\\"#nest_ltgt_r\\\"}]},\\\"nest_parens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#nest_parens\\\"}]},\\\"nest_parens_i\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"include\\\":\\\"#nest_parens_i\\\"}]},\\\"nest_parens_r\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regex_sub\\\"},{\\\"include\\\":\\\"#nest_parens_r\\\"}]},\\\"regex_sub\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolated_ruby\\\"},{\\\"include\\\":\\\"#escaped_char\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arbitrary-repetition.ruby\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arbitrary-repetition.ruby\\\"}},\\\"match\\\":\\\"({)\\\\\\\\d+(,\\\\\\\\d+)?(})\\\",\\\"name\\\":\\\"string.regexp.arbitrary-repetition.ruby\\\"},{\\\"begin\\\":\\\"\\\\\\\\[(?:\\\\\\\\^?])?\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.character-class.ruby\\\"}},\\\"end\\\":\\\"]\\\",\\\"name\\\":\\\"string.regexp.character-class.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escaped_char\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\?#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.ruby\\\"}},\\\"name\\\":\\\"comment.line.number-sign.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escaped_char\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.ruby\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"string.regexp.group.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regex_sub\\\"}]},{\\\"begin\\\":\\\"(?<=^|\\\\\\\\s)(#)\\\\\\\\s(?=[[a-zA-Z0-9,. \\\\\\\\t?!-][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]*$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.ruby\\\"}},\\\"comment\\\":\\\"We are restrictive in what we allow to go after the comment character to avoid false positives, since the availability of comments depend on regexp flags.\\\",\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.ruby\\\"}},\\\"name\\\":\\\"comment.line.number-sign.ruby\\\"}]},\\\"yard\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#yard_comment\\\"},{\\\"include\\\":\\\"#yard_param_types\\\"},{\\\"include\\\":\\\"#yard_option\\\"},{\\\"include\\\":\\\"#yard_tag\\\"},{\\\"include\\\":\\\"#yard_types\\\"},{\\\"include\\\":\\\"#yard_directive\\\"},{\\\"include\\\":\\\"#yard_see\\\"},{\\\"include\\\":\\\"#yard_macro_attribute\\\"}]},\\\"yard_comment\\\":{\\\"begin\\\":\\\"^(\\\\\\\\s*)(#)(\\\\\\\\s*)(@)(abstract|api|author|deprecated|example|macro|note|overload|since|todo|version)(?=\\\\\\\\s|$)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.comment.ruby\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.line.keyword.punctuation.yard.ruby\\\"},\\\"5\\\":{\\\"name\\\":\\\"comment.line.keyword.yard.ruby\\\"}},\\\"comment\\\":\\\"For YARD tags that follow the tag-comment pattern\\\",\\\"contentName\\\":\\\"comment.line.string.yard.ruby\\\",\\\"end\\\":\\\"^(?!\\\\\\\\s*#\\\\\\\\3\\\\\\\\s{2,}|\\\\\\\\s*#\\\\\\\\s*$)\\\",\\\"name\\\":\\\"comment.line.number-sign.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#yard\\\"},{\\\"include\\\":\\\"#yard_continuation\\\"}]},\\\"yard_continuation\\\":{\\\"match\\\":\\\"^\\\\\\\\s*#\\\",\\\"name\\\":\\\"punctuation.definition.comment.ruby\\\"},\\\"yard_directive\\\":{\\\"begin\\\":\\\"^(\\\\\\\\s*)(#)(\\\\\\\\s*)(@!)(endgroup|group|method|parse|scope|visibility)(\\\\\\\\s+((\\\\\\\\[).+(])))?(?=\\\\\\\\s)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.comment.ruby\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.line.keyword.punctuation.yard.ruby\\\"},\\\"5\\\":{\\\"name\\\":\\\"comment.line.keyword.yard.ruby\\\"},\\\"7\\\":{\\\"name\\\":\\\"comment.line.type.yard.ruby\\\"},\\\"8\\\":{\\\"name\\\":\\\"comment.line.punctuation.yard.ruby\\\"},\\\"9\\\":{\\\"name\\\":\\\"comment.line.punctuation.yard.ruby\\\"}},\\\"comment\\\":\\\"For YARD directives\\\",\\\"contentName\\\":\\\"comment.line.string.yard.ruby\\\",\\\"end\\\":\\\"^(?!\\\\\\\\s*#\\\\\\\\3\\\\\\\\s{2,}|\\\\\\\\s*#\\\\\\\\s*$)\\\",\\\"name\\\":\\\"comment.line.number-sign.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#yard\\\"},{\\\"include\\\":\\\"#yard_continuation\\\"}]},\\\"yard_macro_attribute\\\":{\\\"begin\\\":\\\"^(\\\\\\\\s*)(#)(\\\\\\\\s*)(@!)(attribute|macro)(\\\\\\\\s+((\\\\\\\\[).+(])))?(?=\\\\\\\\s)(\\\\\\\\s+([a-z_]\\\\\\\\w*:?))?\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.comment.ruby\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.line.keyword.punctuation.yard.ruby\\\"},\\\"5\\\":{\\\"name\\\":\\\"comment.line.keyword.yard.ruby\\\"},\\\"7\\\":{\\\"name\\\":\\\"comment.line.type.yard.ruby\\\"},\\\"8\\\":{\\\"name\\\":\\\"comment.line.punctuation.yard.ruby\\\"},\\\"9\\\":{\\\"name\\\":\\\"comment.line.punctuation.yard.ruby\\\"},\\\"11\\\":{\\\"name\\\":\\\"comment.line.parameter.yard.ruby\\\"}},\\\"comment\\\":\\\"separate rule for attribute and macro tags because name goes after []\\\",\\\"contentName\\\":\\\"comment.line.string.yard.ruby\\\",\\\"end\\\":\\\"^(?!\\\\\\\\s*#\\\\\\\\3\\\\\\\\s{2,}|\\\\\\\\s*#\\\\\\\\s*$)\\\",\\\"name\\\":\\\"comment.line.number-sign.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#yard\\\"},{\\\"include\\\":\\\"#yard_continuation\\\"}]},\\\"yard_option\\\":{\\\"begin\\\":\\\"^(\\\\\\\\s*)(#)(\\\\\\\\s*)(@)(option)(?=\\\\\\\\s)(?>\\\\\\\\s+([a-z_]\\\\\\\\w*:?))?(?>\\\\\\\\s+((\\\\\\\\[).+(])))?(?>\\\\\\\\s+((\\\\\\\\S*)))?(?>\\\\\\\\s+((\\\\\\\\().+(\\\\\\\\))))?\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.comment.ruby\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.line.keyword.punctuation.yard.ruby\\\"},\\\"5\\\":{\\\"name\\\":\\\"comment.line.keyword.yard.ruby\\\"},\\\"6\\\":{\\\"name\\\":\\\"comment.line.parameter.yard.ruby\\\"},\\\"7\\\":{\\\"name\\\":\\\"comment.line.type.yard.ruby\\\"},\\\"8\\\":{\\\"name\\\":\\\"comment.line.punctuation.yard.ruby\\\"},\\\"9\\\":{\\\"name\\\":\\\"comment.line.punctuation.yard.ruby\\\"},\\\"10\\\":{\\\"name\\\":\\\"comment.line.keyword.yard.ruby\\\"},\\\"11\\\":{\\\"name\\\":\\\"comment.line.hashkey.yard.ruby\\\"},\\\"12\\\":{\\\"name\\\":\\\"comment.line.defaultvalue.yard.ruby\\\"},\\\"13\\\":{\\\"name\\\":\\\"comment.line.punctuation.yard.ruby\\\"},\\\"14\\\":{\\\"name\\\":\\\"comment.line.punctuation.yard.ruby\\\"}},\\\"comment\\\":\\\"For YARD option tag that follow the tag-name-types-key-(value)-description pattern\\\",\\\"contentName\\\":\\\"comment.line.string.yard.ruby\\\",\\\"end\\\":\\\"^(?!\\\\\\\\s*#\\\\\\\\3\\\\\\\\s{2,}|\\\\\\\\s*#\\\\\\\\s*$)\\\",\\\"name\\\":\\\"comment.line.number-sign.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#yard\\\"},{\\\"include\\\":\\\"#yard_continuation\\\"}]},\\\"yard_param_types\\\":{\\\"begin\\\":\\\"^(\\\\\\\\s*)(#)(\\\\\\\\s*)(@)(attr|attr_reader|attr_writer|yieldparam|param)(?=\\\\\\\\s)(?>\\\\\\\\s+(?>([a-z_]\\\\\\\\w*:?)|((\\\\\\\\[).+(]))))?(?>\\\\\\\\s+(?>((\\\\\\\\[).+(]))|([a-z_]\\\\\\\\w*:?)))?\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.comment.ruby\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.line.keyword.punctuation.yard.ruby\\\"},\\\"5\\\":{\\\"name\\\":\\\"comment.line.keyword.yard.ruby\\\"},\\\"6\\\":{\\\"name\\\":\\\"comment.line.parameter.yard.ruby\\\"},\\\"7\\\":{\\\"name\\\":\\\"comment.line.type.yard.ruby\\\"},\\\"8\\\":{\\\"name\\\":\\\"comment.line.punctuation.yard.ruby\\\"},\\\"9\\\":{\\\"name\\\":\\\"comment.line.punctuation.yard.ruby\\\"},\\\"10\\\":{\\\"name\\\":\\\"comment.line.type.yard.ruby\\\"},\\\"11\\\":{\\\"name\\\":\\\"comment.line.punctuation.yard.ruby\\\"},\\\"12\\\":{\\\"name\\\":\\\"comment.line.punctuation.yard.ruby\\\"},\\\"13\\\":{\\\"name\\\":\\\"comment.line.parameter.yard.ruby\\\"}},\\\"comment\\\":\\\"For YARD tags that follow the tag-name-types-description or tag-types-name-description pattern\\\",\\\"contentName\\\":\\\"comment.line.string.yard.ruby\\\",\\\"end\\\":\\\"^(?!\\\\\\\\s*#\\\\\\\\3\\\\\\\\s{2,}|\\\\\\\\s*#\\\\\\\\s*$)\\\",\\\"name\\\":\\\"comment.line.number-sign.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#yard\\\"},{\\\"include\\\":\\\"#yard_continuation\\\"}]},\\\"yard_see\\\":{\\\"begin\\\":\\\"^(\\\\\\\\s*)(#)(\\\\\\\\s*)(@)(see)(?=\\\\\\\\s)(\\\\\\\\s+(.+?))?(?=\\\\\\\\s|$)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.comment.ruby\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.line.keyword.punctuation.yard.ruby\\\"},\\\"5\\\":{\\\"name\\\":\\\"comment.line.keyword.yard.ruby\\\"},\\\"7\\\":{\\\"name\\\":\\\"comment.line.parameter.yard.ruby\\\"}},\\\"comment\\\":\\\"separate rule for @see because name could contain url\\\",\\\"contentName\\\":\\\"comment.line.string.yard.ruby\\\",\\\"end\\\":\\\"^(?!\\\\\\\\s*#\\\\\\\\3\\\\\\\\s{2,}|\\\\\\\\s*#\\\\\\\\s*$)\\\",\\\"name\\\":\\\"comment.line.number-sign.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#yard\\\"},{\\\"include\\\":\\\"#yard_continuation\\\"}]},\\\"yard_tag\\\":{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.comment.ruby\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.line.keyword.punctuation.yard.ruby\\\"},\\\"5\\\":{\\\"name\\\":\\\"comment.line.keyword.yard.ruby\\\"}},\\\"comment\\\":\\\"For YARD tags that are just the tag\\\",\\\"match\\\":\\\"^(\\\\\\\\s*)(#)(\\\\\\\\s*)(@)(private)$\\\",\\\"name\\\":\\\"comment.line.number-sign.ruby\\\"},\\\"yard_types\\\":{\\\"begin\\\":\\\"^(\\\\\\\\s*)(#)(\\\\\\\\s*)(@)(raise|return|yield(?:return)?)(?=\\\\\\\\s)(\\\\\\\\s+((\\\\\\\\[).+(])))?\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.comment.ruby\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.line.keyword.punctuation.yard.ruby\\\"},\\\"5\\\":{\\\"name\\\":\\\"comment.line.keyword.yard.ruby\\\"},\\\"7\\\":{\\\"name\\\":\\\"comment.line.type.yard.ruby\\\"},\\\"8\\\":{\\\"name\\\":\\\"comment.line.punctuation.yard.ruby\\\"},\\\"9\\\":{\\\"name\\\":\\\"comment.line.punctuation.yard.ruby\\\"}},\\\"comment\\\":\\\"For YARD tags that follow the tag-types-comment pattern\\\",\\\"contentName\\\":\\\"comment.line.string.yard.ruby\\\",\\\"end\\\":\\\"^(?!\\\\\\\\s*#\\\\\\\\3\\\\\\\\s{2,}|\\\\\\\\s*#\\\\\\\\s*$)\\\",\\\"name\\\":\\\"comment.line.number-sign.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#yard\\\"},{\\\"include\\\":\\\"#yard_continuation\\\"}]}},\\\"scopeName\\\":\\\"source.ruby\\\",\\\"embeddedLangs\\\":[\\\"html\\\",\\\"haml\\\",\\\"xml\\\",\\\"sql\\\",\\\"graphql\\\",\\\"css\\\",\\\"cpp\\\",\\\"c\\\",\\\"javascript\\\",\\\"shellscript\\\",\\\"lua\\\",\\\"yaml\\\"],\\\"aliases\\\":[\\\"rb\\\"]}\"))\n\nexport default [\n...html,\n...haml,\n...xml,\n...sql,\n...graphql,\n...css,\n...cpp,\n...c,\n...javascript,\n...shellscript,\n...lua,\n...yaml,\nlang\n]\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,IAAM,OAAO,OAAO,OAAO,KAAK,MAAM,6kjDAAwouD,CAAC;AAE/quD,IAAO,eAAQ;AAAA,EACf,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACA;", "names": []}