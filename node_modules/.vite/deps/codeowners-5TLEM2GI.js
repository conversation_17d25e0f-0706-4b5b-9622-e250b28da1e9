import "./chunk-DP4XHQAG.js";

// node_modules/@shikijs/langs/dist/codeowners.mjs
var lang = Object.freeze(JSON.parse('{"displayName":"CODEOWNERS","name":"codeowners","patterns":[{"include":"#comment"},{"include":"#pattern"},{"include":"#owner"}],"repository":{"comment":{"patterns":[{"begin":"^\\\\s*#","captures":{"0":{"name":"punctuation.definition.comment.codeowners"}},"end":"$","name":"comment.line.codeowners"}]},"owner":{"match":"\\\\S*@\\\\S+","name":"storage.type.function.codeowners"},"pattern":{"match":"^\\\\s*(\\\\S+)","name":"variable.other.codeowners"}},"scopeName":"text.codeowners"}'));
var codeowners_default = [
  lang
];
export {
  codeowners_default as default
};
//# sourceMappingURL=codeowners-5TLEM2GI.js.map
