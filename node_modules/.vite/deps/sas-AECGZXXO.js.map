{"version": 3, "sources": ["../../@shikijs/langs/dist/sas.mjs"], "sourcesContent": ["import sql from './sql.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"SAS\\\",\\\"fileTypes\\\":[\\\"sas\\\"],\\\"foldingStartMarker\\\":\\\"(?i:(proc|data|%macro).*;$)\\\",\\\"foldingStopMarker\\\":\\\"(?i:(run|quit|%mend)\\\\\\\\s?);\\\",\\\"name\\\":\\\"sas\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#starComment\\\"},{\\\"include\\\":\\\"#blockComment\\\"},{\\\"include\\\":\\\"#macro\\\"},{\\\"include\\\":\\\"#constant\\\"},{\\\"include\\\":\\\"#quote\\\"},{\\\"include\\\":\\\"#operator\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(?i:(data))\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.sas\\\"}},\\\"comment\\\":\\\"Begins a DATA step and provides names for any output SAS data sets, views, or programs.\\\",\\\"end\\\":\\\"(;)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#blockComment\\\"},{\\\"include\\\":\\\"#dataSet\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.sas\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.sas\\\"}},\\\"match\\\":\\\"(?i:(?:(stack|pgm|view|source)\\\\\\\\s?=\\\\\\\\s?)|(debug|nesting|nolist))\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(?i:(set|update|modify|merge))\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.sas\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.class.sas\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.class.sas\\\"}},\\\"comment\\\":\\\"DATA set File-Handling Statements for DATA step\\\",\\\"end\\\":\\\"(;)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#blockComment\\\"},{\\\"include\\\":\\\"#dataSet\\\"}]},{\\\"match\\\":\\\"(?i:\\\\\\\\b(if|while|until|for|do|end|then|else|run|quit|cancel|options)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.control.sas\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.class.sas\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.sas\\\"}},\\\"match\\\":\\\"(?i:(%(bquote|do|else|end|eval|global|goto|if|inc|include|index|input|length|let|list|local|lowcase|macro|mend|nrbquote|nrquote|nrstr|put|qscan|qsysfunc|quote|run|scan|str|substr|syscall|sysevalf|sysexec|sysfunc|sysrc|then|to|unquote|upcase|until|while|window)\\\\\\\\b))\\\\\\\\s*(\\\\\\\\w*)\\\",\\\"name\\\":\\\"keyword.other.sas\\\"},{\\\"begin\\\":\\\"(?i:\\\\\\\\b(proc\\\\\\\\s*(sql))\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.sas\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.class.sas\\\"}},\\\"comment\\\":\\\"Looks like for this to work there must be a *name* as well as the patterns/include bit.\\\",\\\"end\\\":\\\"(?i:\\\\\\\\b(quit)\\\\\\\\s*;)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.sas\\\"}},\\\"name\\\":\\\"meta.sql.sas\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#starComment\\\"},{\\\"include\\\":\\\"#blockComment\\\"},{\\\"include\\\":\\\"source.sql\\\"}]},{\\\"match\\\":\\\"(?i:\\\\\\\\b(by|label|format)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.datastep.sas\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.sas\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.class.sas\\\"}},\\\"match\\\":\\\"(?i:\\\\\\\\b(proc (\\\\\\\\w+))\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.function-call.sas\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(_n_|_error_)\\\\\\\\b)\\\",\\\"name\\\":\\\"variable.language.sas\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.class.sas\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?i:(_all_|_character_|_cmd_|_freq_|_i_|_infile_|_last_|_msg_|_null_|_numeric_|_temporary_|_type_|abort|abs|addr|adjrsq|airy|alpha|alter|altlog|altprint|and|arcos|array|arsin|as|atan|attrc|attrib|attrn|authserver|autoexec|awscontrol|awsdef|awsmenu|awsmenumerge|awstitle|backward|band|base|betainv|between|blocksize|blshift|bnot|bor|brshift|bufno|bufsize|bxor|by|byerr|byline|byte|calculated|call|cards|cards4|case|catcache|cbufno|cdf|ceil|center|cexist|change|chisq|cinv|class|cleanup|close|cnonct|cntllev|coalesce|codegen|col|collate|collin|column|comamid|comaux1|comaux2|comdef|compbl|compound|compress|config|continue|convert|cos|cosh|cpuid|create|cross|crosstab|css|curobs|cv|daccdb|daccdbsl|daccsl|daccsyd|dacctab|dairy|datalines|datalines4|date|datejul|datepart|datetime|day|dbcslang|dbcstype|dclose|ddm|delete|delimiter|depdb|depdbsl|depsl|depsyd|deptab|dequote|descending|descript|design=|device|dflang|dhms|dif|digamma|dim|dinfo|display|distinct|dkricond|dkrocond|dlm|dnum|do|dopen|doptname|doptnum|dread|drop|dropnote|dsname|dsnferr|echo|else|emaildlg|emailid|emailpw|emailserver|emailsys|encrypt|end|endsas|engine|eof|eov|erf|erfc|error|errorcheck|errors|exist|exp|fappend|fclose|fcol|fdelete|feedback|fetch|fetchobs|fexist|fget|file|fileclose|fileexist|filefmt|filename|fileref|filevar|finfo|finv|fipname|fipnamel|fipstate|first|firstobs|floor|fmterr|fmtsearch|fnonct|fnote|font|fontalias|footnote[1-9]?|fopen|foptname|foptnum|force|formatted|formchar|formdelim|formdlim|forward|fpoint|fpos|fput|fread|frewind|frlen|from|fsep|full|fullstimer|fuzz|fwrite|gaminv|gamma|getoption|getvarc|getvarn|go|goto|group|gwindow|hbar|hbound|helpenv|helploc|hms|honorappearance|hosthelp|hostprint|hour|hpct|html|hvar|ibessel|ibr|id|if|index|indexc|indexw|infile|informat|initcmd|initstmt|inner|input|inputc|inputn|inr|insert|int|intck|intnx|into|intrr|invaliddata|irr|is|jbessel|join|juldate|keep|kentb|kurtosis|label|lag|last|lbound|leave|left|length|levels|lgamma|lib|libname|library|libref|line|linesize|link|list|log|log10|log2|logpdf|logpmf|logsdf|lostcard|lowcase|lrecl|ls|macro|macrogen|maps|mautosource|max|maxdec|maxr|mdy|mean|measures|median|memtype|merge|merror|min|minute|missing|missover|mlogic|mod|mode|model|modify|month|mopen|mort|mprint|mrecall|msglevel|msymtabmax|mvarsize|myy|n|nest|netpv|new|news|nmiss|no|nobatch|nobs|nocaps|nocardimage|nocenter|nocharcode|nocmdmac|nocol|nocum|nodate|nodbcs|nodetails|nodmr|nodms|nodmsbatch|nodup|nodupkey|noduplicates|noechoauto|noequals|noerrorabend|noexitwindows|nofullstimer|noicon|noimplmac|noint|nolist|noloadlist|nomiss|nomlogic|nomprint|nomrecall|nomsgcase|nomstored|nomultenvappl|nonotes|nonumber|noobs|noovp|nopad|nopercent|noprint|noprintinit|normal|norow|norsasuser|nosetinit|nosource|nosource2|nosplash|nosymbolgen|note|notes|notitle|notitles|notsorted|noverbose|noxsync|noxwait|npv|null|number|numkeys|nummousekeys|nway|obs|ods|on|open|option|order|ordinal|otherwise|out|outer|outp=|output|over|ovp|p(1|5|10|25|50|75|90|95|99)|pad|pad2|page|pageno|pagesize|paired|parm|parmcards|path|pathdll|pathname|pdf|peek|peekc|pfkey|pmf|point|poisson|poke|position|printer|probbeta|probbnml|probchi|probf|probgam|probhypr|probit|probnegb|probnorm|probsig|probt|procleave|project|prt|propcase|prxmatch|prxparse|prxchange|prxposn|ps|put|putc|putn|pw|pwreq|qtr|quote|r|ranbin|rancau|ranexp|rangam|range|ranks|rannor|ranpoi|rantbl|rantri|ranuni|read|recfm|register|regr|remote|remove|rename|repeat|replace|resolve|retain|return|reuse|reverse|rewind|right|round|rsquare|rtf|rtrace|rtraceloc|s|s2|samploc|sasautos|sascontrol|sasfrscr|sashelp|sasmsg|sasmstore|sasscript|sasuser|saving|scan|sdf|second|select|selection|separated|seq|serror|set|setcomm|setot|sign|simple|sin|sinh|siteinfo|skewness|skip|sle|sls|sortedby|sortpgm|sortseq|sortsize|soundex|source2|spedis|splashlocation|split|spool|sqrt|start|std|stderr|stdin|stfips|stimer|stname|stnamel|stop|stopover|strip|subgroup|subpopn|substr|sum|sumwgt|symbol|symbolgen|symget|symput|sysget|sysin|sysleave|sysmsg|sysparm|sysprint|sysprintfont|sysprod|sysrc|system|t|table|tables|tan|tanh|tapeclose|tbufsize|terminal|test|then|time|timepart|tinv|title[1-9]?|tnonct|to|today|tol|tooldef|totper|transformout|translate|trantab|tranwrd|trigamma|trim|trimn|trunc|truncover|type|unformatted|uniform|union|until|upcase|update|user|usericon|uss|validate|value|var|varfmt|varinfmt|varlabel|varlen|varname|varnum|varray|varrayx|vartype|verify|vformat|vformatd|vformatdx|vformatn|vformatnx|vformatw|vformatwx|vformatx|vinarray|vinarrayx|vinformat|vinformatd|vinformatdx|vinformatn|vinformatnx|vinformatw|vinformatwx|vinformatx|vlabel|vlabelx|vlength|vlengthx|vname|vnamex|vnferr|vtype|vtypex|weekday|weight|when|where|while|wincharset|window|work|workinit|workterm|write|wsum|wsumx|x|xsync|xwait|year|yearcutoff|yes|yyq|zipfips|zipname|zipnamel|zipstate))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.sas\\\"}],\\\"repository\\\":{\\\"blockComment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.slashstar.sas\\\"}]},\\\"constant\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"numeric constant\\\",\\\"match\\\":\\\"(?<![&\\\\\\\\}])\\\\\\\\b[0-9]*\\\\\\\\.?[0-9]+([eEdD][-+]?[0-9]+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.sas\\\"},{\\\"comment\\\":\\\"single quote numeric-type constant\\\",\\\"match\\\":\\\"(')([^']+)(')(dt|[dt])\\\",\\\"name\\\":\\\"constant.numeric.quote.single.sas\\\"},{\\\"comment\\\":\\\"double quote numeric-type constant\\\",\\\"match\\\":\\\"(\\\\\\\")([^\\\\\\\"]+)(\\\\\\\")(dt|[dt])\\\",\\\"name\\\":\\\"constant.numeric.quote.double.sas\\\"}]},\\\"dataSet\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"((\\\\\\\\w+)\\\\\\\\.)?(\\\\\\\\w+)\\\\\\\\s?\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"entity.name.class.libref.sas\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.class.dsname.sas\\\"}},\\\"comment\\\":\\\"data set with options\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#dataSetOptions\\\"},{\\\"include\\\":\\\"#blockComment\\\"},{\\\"include\\\":\\\"#macro\\\"},{\\\"include\\\":\\\"#constant\\\"},{\\\"include\\\":\\\"#quote\\\"},{\\\"include\\\":\\\"#operator\\\"}]},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"entity.name.class.libref.sas\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.class.dsname.sas\\\"}},\\\"comment\\\":\\\"data set without options\\\",\\\"match\\\":\\\"\\\\\\\\b((\\\\\\\\w+)\\\\\\\\.)?(\\\\\\\\w+)\\\\\\\\b\\\"}]},\\\"dataSetOptions\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\s|\\\\\\\\(|\\\\\\\\))(?i:ALTER|BUFNO|BUFSIZE|CNTLLEV|COMPRESS|DLDMGACTION|ENCRYPT|ENCRYPTKEY|EXTENDOBSCOUNTER|GENMAX|GENNUM|INDEX|LABEL|OBSBUF|OUTREP|PW|PWREQ|READ|REPEMPTY|REPLACE|REUSE|ROLE|SORTEDBY|SPILL|TOBSNO|TYPE|WRITE|FILECLOSE|FIRSTOBS|IN|OBS|POINTOBS|WHERE|WHEREUP|IDXNAME|IDXWHERE|DROP|KEEP|RENAME)\\\\\\\\s?=\\\",\\\"name\\\":\\\"keyword.other.sas\\\"}]},\\\"macro\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(&+(?i:[a-z_]([a-z0-9_]+)?)(\\\\\\\\.+)?)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.macro.sas\\\"}]},\\\"operator\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"([\\\\\\\\+\\\\\\\\-\\\\\\\\*\\\\\\\\^\\\\\\\\/])\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.sas\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:(eq|ne|gt|lt|ge|le|in|not|&|and|or|min|max))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.comparison.sas\\\"},{\\\"match\\\":\\\"([¬<>^~]?=(:)?|>|<|\\\\\\\\||!|¦|¬|^|~|<>|><|\\\\\\\\|\\\\\\\\|)\\\",\\\"name\\\":\\\"keyword.operator.sas\\\"}]},\\\"quote\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<!%)(')\\\",\\\"comment\\\":\\\"single quoted string block\\\",\\\"end\\\":\\\"(')([bx])?\\\",\\\"name\\\":\\\"string.quoted.single.sas\\\"},{\\\"begin\\\":\\\"(\\\\\\\")\\\",\\\"comment\\\":\\\"double quoted string block\\\",\\\"end\\\":\\\"(\\\\\\\")([bx])?\\\",\\\"name\\\":\\\"string.quoted.double.sas\\\"}]},\\\"starComment\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#blockcomment\\\"},{\\\"begin\\\":\\\"(?<=;)[\\\\\\\\s%]*\\\\\\\\*\\\",\\\"end\\\":\\\";\\\",\\\"name\\\":\\\"comment.line.inline.star.sas\\\"},{\\\"begin\\\":\\\"^[\\\\\\\\s%]*\\\\\\\\*\\\",\\\"end\\\":\\\";\\\",\\\"name\\\":\\\"comment.line.start.sas\\\"}]}},\\\"scopeName\\\":\\\"source.sas\\\",\\\"embeddedLangs\\\":[\\\"sql\\\"]}\"))\n\nexport default [\n...sql,\nlang\n]\n"], "mappings": ";;;;;;AAEA,IAAM,OAAO,OAAO,OAAO,KAAK,MAAM,29SAAw/T,CAAC;AAE/hU,IAAO,cAAQ;AAAA,EACf,GAAG;AAAA,EACH;AACA;", "names": []}