{"version": 3, "sources": ["../../@shikijs/langs/dist/jsx.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"JSX\\\",\\\"name\\\":\\\"jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#directives\\\"},{\\\"include\\\":\\\"#statements\\\"},{\\\"include\\\":\\\"#shebang\\\"}],\\\"repository\\\":{\\\"access-modifier\\\":{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(abstract|declare|override|public|protected|private|readonly|static)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"after-operator-block-as-object-literal\\\":{\\\"begin\\\":\\\"(?<!\\\\\\\\+\\\\\\\\+|--)(?<=[:=(,\\\\\\\\[?+!>]|^await|[^\\\\\\\\._$[:alnum:]]await|^return|[^\\\\\\\\._$[:alnum:]]return|^yield|[^\\\\\\\\._$[:alnum:]]yield|^throw|[^\\\\\\\\._$[:alnum:]]throw|^in|[^\\\\\\\\._$[:alnum:]]in|^of|[^\\\\\\\\._$[:alnum:]]of|^typeof|[^\\\\\\\\._$[:alnum:]]typeof|&&|\\\\\\\\|\\\\\\\\||\\\\\\\\*)\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"name\\\":\\\"meta.objectliteral.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#object-member\\\"}]},\\\"array-binding-pattern\\\":{\\\"begin\\\":\\\"(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.array.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.array.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#binding-element\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"array-binding-pattern-const\\\":{\\\"begin\\\":\\\"(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.array.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.array.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#binding-element-const\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"array-literal\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.brace.square.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.square.js.jsx\\\"}},\\\"name\\\":\\\"meta.array.literal.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"arrow-function\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.js.jsx\\\"}},\\\"match\\\":\\\"(?:(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(\\\\\\\\basync)\\\\\\\\s+)?([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(?==>)\\\",\\\"name\\\":\\\"meta.arrow.js.jsx\\\"},{\\\"begin\\\":\\\"(?:(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(\\\\\\\\basync))?((?<![})!\\\\\\\\]])\\\\\\\\s*(?=((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?[(]\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([)]\\\\\\\\s*:)|((\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*)?[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*:)))|([<]\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s+extends\\\\\\\\s*[^=>])|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?\\\\\\\\(\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([_$[:alpha:]]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*[_$[:alpha:]]))([^()\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))*)?\\\\\\\\)(\\\\\\\\s*:\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+)?\\\\\\\\s*=>)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"}},\\\"end\\\":\\\"(?==>|\\\\\\\\{|(^\\\\\\\\s*(export|function|class|interface|let|var|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|const|import|enum|namespace|module|type|abstract|declare)\\\\\\\\s+))\\\",\\\"name\\\":\\\"meta.arrow.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"include\\\":\\\"#function-parameters\\\"},{\\\"include\\\":\\\"#arrow-return-type\\\"},{\\\"include\\\":\\\"#possibly-arrow-return-type\\\"}]},{\\\"begin\\\":\\\"=>\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"storage.type.function.arrow.js.jsx\\\"}},\\\"end\\\":\\\"((?<=\\\\\\\\}|\\\\\\\\S)(?<!=>)|((?!\\\\\\\\{)(?=\\\\\\\\S)))(?!\\\\\\\\/[\\\\\\\\/\\\\\\\\*])\\\",\\\"name\\\":\\\"meta.arrow.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#single-line-comment-consuming-line-ending\\\"},{\\\"include\\\":\\\"#decl-block\\\"},{\\\"include\\\":\\\"#expression\\\"}]}]},\\\"arrow-return-type\\\":{\\\"begin\\\":\\\"(?<=\\\\\\\\))\\\\\\\\s*(:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.type.annotation.js.jsx\\\"}},\\\"end\\\":\\\"(?==>|\\\\\\\\{|(^\\\\\\\\s*(export|function|class|interface|let|var|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|const|import|enum|namespace|module|type|abstract|declare)\\\\\\\\s+))\\\",\\\"name\\\":\\\"meta.return.type.arrow.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#arrow-return-type-body\\\"}]},\\\"arrow-return-type-body\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=[:])(?=\\\\\\\\s*\\\\\\\\{)\\\",\\\"end\\\":\\\"(?<=\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-object\\\"}]},{\\\"include\\\":\\\"#type-predicate-operator\\\"},{\\\"include\\\":\\\"#type\\\"}]},\\\"async-modifier\\\":{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(async)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"},\\\"binding-element\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#numeric-literal\\\"},{\\\"include\\\":\\\"#regex\\\"},{\\\"include\\\":\\\"#object-binding-pattern\\\"},{\\\"include\\\":\\\"#array-binding-pattern\\\"},{\\\"include\\\":\\\"#destructuring-variable-rest\\\"},{\\\"include\\\":\\\"#variable-initializer\\\"}]},\\\"binding-element-const\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#numeric-literal\\\"},{\\\"include\\\":\\\"#regex\\\"},{\\\"include\\\":\\\"#object-binding-pattern-const\\\"},{\\\"include\\\":\\\"#array-binding-pattern-const\\\"},{\\\"include\\\":\\\"#destructuring-variable-rest-const\\\"},{\\\"include\\\":\\\"#variable-initializer\\\"}]},\\\"boolean-literal\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))true(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"constant.language.boolean.true.js.jsx\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))false(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"constant.language.boolean.false.js.jsx\\\"}]},\\\"brackets\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"{\\\",\\\"end\\\":\\\"}|(?=\\\\\\\\*/)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#brackets\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"end\\\":\\\"\\\\\\\\]|(?=\\\\\\\\*/)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#brackets\\\"}]}]},\\\"cast\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#jsx\\\"}]},\\\"class-declaration\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b(?:(abstract)\\\\\\\\s+)?\\\\\\\\b(class)\\\\\\\\b(?=\\\\\\\\s+|/[/*])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.class.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})\\\",\\\"name\\\":\\\"meta.class.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#class-declaration-or-expression-patterns\\\"}]},\\\"class-declaration-or-expression-patterns\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#class-or-interface-heritage\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.type.class.js.jsx\\\"}},\\\"match\\\":\\\"[_$[:alpha:]][_$[:alnum:]]*\\\"},{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"include\\\":\\\"#class-or-interface-body\\\"}]},\\\"class-expression\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(abstract)\\\\\\\\s+)?(class)\\\\\\\\b(?=\\\\\\\\s+|[<{]|\\\\\\\\/[\\\\\\\\/*])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.class.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})\\\",\\\"name\\\":\\\"meta.class.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#class-declaration-or-expression-patterns\\\"}]},\\\"class-or-interface-body\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"begin\\\":\\\"(?<=:)\\\\\\\\s*\\\",\\\"end\\\":\\\"(?=\\\\\\\\s|[;),}\\\\\\\\]:\\\\\\\\-\\\\\\\\+]|;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},{\\\"include\\\":\\\"#method-declaration\\\"},{\\\"include\\\":\\\"#indexer-declaration\\\"},{\\\"include\\\":\\\"#field-declaration\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#type-annotation\\\"},{\\\"include\\\":\\\"#variable-initializer\\\"},{\\\"include\\\":\\\"#access-modifier\\\"},{\\\"include\\\":\\\"#property-accessor\\\"},{\\\"include\\\":\\\"#async-modifier\\\"},{\\\"include\\\":\\\"#after-operator-block-as-object-literal\\\"},{\\\"include\\\":\\\"#decl-block\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"},{\\\"include\\\":\\\"#punctuation-semicolon\\\"}]},\\\"class-or-interface-heritage\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:\\\\\\\\b(extends|implements)\\\\\\\\b)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\{)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#class-or-interface-heritage\\\"},{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"include\\\":\\\"#expressionWithoutIdentifiers\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.module.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.accessor.optional.js.jsx\\\"}},\\\"match\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(?:(\\\\\\\\.)|(\\\\\\\\?\\\\\\\\.(?!\\\\\\\\s*[[:digit:]])))(?=\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*(\\\\\\\\s*\\\\\\\\??\\\\\\\\.\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*)*\\\\\\\\s*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.inherited-class.js.jsx\\\"}},\\\"match\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\"},{\\\"include\\\":\\\"#expressionPunctuations\\\"}]},\\\"comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\\\\\\*(?!/)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.js.jsx\\\"}},\\\"name\\\":\\\"comment.block.documentation.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#docblock\\\"}]},{\\\"begin\\\":\\\"(/\\\\\\\\*)(?:\\\\\\\\s*((@)internal)(?=\\\\\\\\s|(\\\\\\\\*/)))?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.internaldeclaration.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.decorator.internaldeclaration.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.js.jsx\\\"}},\\\"name\\\":\\\"comment.block.js.jsx\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?((//)(?:\\\\\\\\s*((@)internal)(?=\\\\\\\\s|$))?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.line.double-slash.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.comment.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.internaldeclaration.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.decorator.internaldeclaration.js.jsx\\\"}},\\\"contentName\\\":\\\"comment.line.double-slash.js.jsx\\\",\\\"end\\\":\\\"(?=$)\\\"}]},\\\"control-statement\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#switch-statement\\\"},{\\\"include\\\":\\\"#for-loop\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(catch|finally|throw|try)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.control.trycatch.js.jsx\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.loop.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.label.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(break|continue|goto)\\\\\\\\s+([_$[:alpha:]][_$[:alnum:]]*)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(break|continue|do|goto|while)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.control.loop.js.jsx\\\"},{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(return)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.flow.js.jsx\\\"}},\\\"end\\\":\\\"(?=[;}]|$|;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(case|default|switch)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.control.switch.js.jsx\\\"},{\\\"include\\\":\\\"#if-statement\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(else|if)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.control.conditional.js.jsx\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(with)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.control.with.js.jsx\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(package)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.control.js.jsx\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(debugger)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.other.debugger.js.jsx\\\"}]},\\\"decl-block\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"name\\\":\\\"meta.block.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#statements\\\"}]},\\\"declaration\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#var-expr\\\"},{\\\"include\\\":\\\"#function-declaration\\\"},{\\\"include\\\":\\\"#class-declaration\\\"},{\\\"include\\\":\\\"#interface-declaration\\\"},{\\\"include\\\":\\\"#enum-declaration\\\"},{\\\"include\\\":\\\"#namespace-declaration\\\"},{\\\"include\\\":\\\"#type-alias-declaration\\\"},{\\\"include\\\":\\\"#import-equals-declaration\\\"},{\\\"include\\\":\\\"#import-declaration\\\"},{\\\"include\\\":\\\"#export-declaration\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(declare|export)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"storage.modifier.js.jsx\\\"}]},\\\"decorator\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))\\\\\\\\@\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.decorator.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"meta.decorator.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"destructuring-const\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<!=|:|^of|[^\\\\\\\\._$[:alnum:]]of|^in|[^\\\\\\\\._$[:alnum:]]in)\\\\\\\\s*(?=\\\\\\\\{)\\\",\\\"end\\\":\\\"(?=$|^|[;,=}]|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+))\\\",\\\"name\\\":\\\"meta.object-binding-pattern-variable.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#object-binding-pattern-const\\\"},{\\\"include\\\":\\\"#type-annotation\\\"},{\\\"include\\\":\\\"#comment\\\"}]},{\\\"begin\\\":\\\"(?<!=|:|^of|[^\\\\\\\\._$[:alnum:]]of|^in|[^\\\\\\\\._$[:alnum:]]in)\\\\\\\\s*(?=\\\\\\\\[)\\\",\\\"end\\\":\\\"(?=$|^|[;,=}]|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+))\\\",\\\"name\\\":\\\"meta.array-binding-pattern-variable.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#array-binding-pattern-const\\\"},{\\\"include\\\":\\\"#type-annotation\\\"},{\\\"include\\\":\\\"#comment\\\"}]}]},\\\"destructuring-parameter\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<!=|:)\\\\\\\\s*(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.object.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.object.js.jsx\\\"}},\\\"name\\\":\\\"meta.parameter.object-binding-pattern.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parameter-object-binding-element\\\"}]},{\\\"begin\\\":\\\"(?<!=|:)\\\\\\\\s*(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.array.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.array.js.jsx\\\"}},\\\"name\\\":\\\"meta.paramter.array-binding-pattern.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parameter-binding-element\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]}]},\\\"destructuring-parameter-rest\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.js.jsx\\\"}},\\\"match\\\":\\\"(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?([_$[:alpha:]][_$[:alnum:]]*)\\\"},\\\"destructuring-variable\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<!=|:|^of|[^\\\\\\\\._$[:alnum:]]of|^in|[^\\\\\\\\._$[:alnum:]]in)\\\\\\\\s*(?=\\\\\\\\{)\\\",\\\"end\\\":\\\"(?=$|^|[;,=}]|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+))\\\",\\\"name\\\":\\\"meta.object-binding-pattern-variable.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#object-binding-pattern\\\"},{\\\"include\\\":\\\"#type-annotation\\\"},{\\\"include\\\":\\\"#comment\\\"}]},{\\\"begin\\\":\\\"(?<!=|:|^of|[^\\\\\\\\._$[:alnum:]]of|^in|[^\\\\\\\\._$[:alnum:]]in)\\\\\\\\s*(?=\\\\\\\\[)\\\",\\\"end\\\":\\\"(?=$|^|[;,=}]|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+))\\\",\\\"name\\\":\\\"meta.array-binding-pattern-variable.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#array-binding-pattern\\\"},{\\\"include\\\":\\\"#type-annotation\\\"},{\\\"include\\\":\\\"#comment\\\"}]}]},\\\"destructuring-variable-rest\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.definition.variable.js.jsx variable.other.readwrite.js.jsx\\\"}},\\\"match\\\":\\\"(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?([_$[:alpha:]][_$[:alnum:]]*)\\\"},\\\"destructuring-variable-rest-const\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.definition.variable.js.jsx variable.other.constant.js.jsx\\\"}},\\\"match\\\":\\\"(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?([_$[:alpha:]][_$[:alnum:]]*)\\\"},\\\"directives\\\":{\\\"begin\\\":\\\"^(///)\\\\\\\\s*(?=<(reference|amd-dependency|amd-module)(\\\\\\\\s+(path|types|no-default-lib|lib|name|resolution-mode)\\\\\\\\s*=\\\\\\\\s*((\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`)))+\\\\\\\\s*/>\\\\\\\\s*$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.js.jsx\\\"}},\\\"end\\\":\\\"(?=$)\\\",\\\"name\\\":\\\"comment.line.triple-slash.directive.js.jsx\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(<)(reference|amd-dependency|amd-module)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag.directive.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.directive.js.jsx\\\"}},\\\"end\\\":\\\"/>\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag.directive.js.jsx\\\"}},\\\"name\\\":\\\"meta.tag.js.jsx\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"path|types|no-default-lib|lib|name|resolution-mode\\\",\\\"name\\\":\\\"entity.other.attribute-name.directive.js.jsx\\\"},{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.js.jsx\\\"},{\\\"include\\\":\\\"#string\\\"}]}]},\\\"docblock\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.language.access-type.jsdoc\\\"}},\\\"match\\\":\\\"((@)(?:access|api))\\\\\\\\s+(private|protected|public)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.type.instance.jsdoc\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.bracket.angle.begin.jsdoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.other.email.link.underline.jsdoc\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.bracket.angle.end.jsdoc\\\"}},\\\"match\\\":\\\"((@)author)\\\\\\\\s+([^@\\\\\\\\s<>*/](?:[^@<>*/]|\\\\\\\\*[^/])*)(?:\\\\\\\\s*(<)([^>\\\\\\\\s]+)(>))?\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.type.instance.jsdoc\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.control.jsdoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.type.instance.jsdoc\\\"}},\\\"match\\\":\\\"((@)borrows)\\\\\\\\s+((?:[^@\\\\\\\\s*/]|\\\\\\\\*[^/])+)\\\\\\\\s+(as)\\\\\\\\s+((?:[^@\\\\\\\\s*/]|\\\\\\\\*[^/])+)\\\"},{\\\"begin\\\":\\\"((@)example)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"}},\\\"end\\\":\\\"(?=@|\\\\\\\\*/)\\\",\\\"name\\\":\\\"meta.example.jsdoc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"^\\\\\\\\s\\\\\\\\*\\\\\\\\s+\\\"},{\\\"begin\\\":\\\"\\\\\\\\G(<)caption(>)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.tag.inline.jsdoc\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.bracket.angle.begin.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.bracket.angle.end.jsdoc\\\"}},\\\"contentName\\\":\\\"constant.other.description.jsdoc\\\",\\\"end\\\":\\\"(</)caption(>)|(?=\\\\\\\\*/)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.tag.inline.jsdoc\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.bracket.angle.begin.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.bracket.angle.end.jsdoc\\\"}}},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"source.embedded.js.jsx\\\"}},\\\"match\\\":\\\"[^\\\\\\\\s@*](?:[^*]|\\\\\\\\*[^/])*\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.language.symbol-type.jsdoc\\\"}},\\\"match\\\":\\\"((@)kind)\\\\\\\\s+(class|constant|event|external|file|function|member|mixin|module|namespace|typedef)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.link.underline.jsdoc\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.instance.jsdoc\\\"}},\\\"match\\\":\\\"((@)see)\\\\\\\\s+(?:((?=https?://)(?:[^\\\\\\\\s*]|\\\\\\\\*[^/])+)|((?!https?://|(?:\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])?{@(?:link|linkcode|linkplain|tutorial)\\\\\\\\b)(?:[^@\\\\\\\\s*/]|\\\\\\\\*[^/])+))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.jsdoc\\\"}},\\\"match\\\":\\\"((@)template)\\\\\\\\s+([A-Za-z_$][\\\\\\\\w$.\\\\\\\\[\\\\\\\\]]*(?:\\\\\\\\s*,\\\\\\\\s*[A-Za-z_$][\\\\\\\\w$.\\\\\\\\[\\\\\\\\]]*)*)\\\"},{\\\"begin\\\":\\\"((@)template)\\\\\\\\s+(?={)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s|\\\\\\\\*/|[^{}\\\\\\\\[\\\\\\\\]A-Za-z_$])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#jsdoctype\\\"},{\\\"match\\\":\\\"([A-Za-z_$][\\\\\\\\w$.\\\\\\\\[\\\\\\\\]]*)\\\",\\\"name\\\":\\\"variable.other.jsdoc\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.jsdoc\\\"}},\\\"match\\\":\\\"((@)(?:arg|argument|const|constant|member|namespace|param|var))\\\\\\\\s+([A-Za-z_$][\\\\\\\\w$.\\\\\\\\[\\\\\\\\]]*)\\\"},{\\\"begin\\\":\\\"((@)typedef)\\\\\\\\s+(?={)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s|\\\\\\\\*/|[^{}\\\\\\\\[\\\\\\\\]A-Za-z_$])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#jsdoctype\\\"},{\\\"match\\\":\\\"(?:[^@\\\\\\\\s*/]|\\\\\\\\*[^/])+\\\",\\\"name\\\":\\\"entity.name.type.instance.jsdoc\\\"}]},{\\\"begin\\\":\\\"((@)(?:arg|argument|const|constant|member|namespace|param|prop|property|var))\\\\\\\\s+(?={)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s|\\\\\\\\*/|[^{}\\\\\\\\[\\\\\\\\]A-Za-z_$])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#jsdoctype\\\"},{\\\"match\\\":\\\"([A-Za-z_$][\\\\\\\\w$.\\\\\\\\[\\\\\\\\]]*)\\\",\\\"name\\\":\\\"variable.other.jsdoc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.optional-value.begin.bracket.square.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.jsdoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"source.embedded.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.optional-value.end.bracket.square.jsdoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"invalid.illegal.syntax.jsdoc\\\"}},\\\"match\\\":\\\"(\\\\\\\\[)\\\\\\\\s*[\\\\\\\\w$]+(?:(?:\\\\\\\\[\\\\\\\\])?\\\\\\\\.[\\\\\\\\w$]+)*(?:\\\\\\\\s*(=)\\\\\\\\s*((?>\\\\\\\"(?:(?:\\\\\\\\*(?!/))|(?:\\\\\\\\\\\\\\\\(?!\\\\\\\"))|[^*\\\\\\\\\\\\\\\\])*?\\\\\\\"|'(?:(?:\\\\\\\\*(?!/))|(?:\\\\\\\\\\\\\\\\(?!'))|[^*\\\\\\\\\\\\\\\\])*?'|\\\\\\\\[(?:(?:\\\\\\\\*(?!/))|[^*])*?\\\\\\\\]|(?:(?:\\\\\\\\*(?!/))|\\\\\\\\s(?!\\\\\\\\s*\\\\\\\\])|\\\\\\\\[.*?(?:\\\\\\\\]|(?=\\\\\\\\*/))|[^*\\\\\\\\s\\\\\\\\[\\\\\\\\]])*)*))?\\\\\\\\s*(?:(\\\\\\\\])((?:[^*\\\\\\\\s]|\\\\\\\\*[^\\\\\\\\s/])+)?|(?=\\\\\\\\*/))\\\",\\\"name\\\":\\\"variable.other.jsdoc\\\"}]},{\\\"begin\\\":\\\"((@)(?:define|enum|exception|export|extends|lends|implements|modifies|namespace|private|protected|returns?|satisfies|suppress|this|throws|type|yields?))\\\\\\\\s+(?={)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s|\\\\\\\\*/|[^{}\\\\\\\\[\\\\\\\\]A-Za-z_$])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#jsdoctype\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.type.instance.jsdoc\\\"}},\\\"match\\\":\\\"((@)(?:alias|augments|callback|constructs|emits|event|fires|exports?|extends|external|function|func|host|lends|listens|interface|memberof!?|method|module|mixes|mixin|name|requires|see|this|typedef|uses))\\\\\\\\s+((?:[^{}@\\\\\\\\s*]|\\\\\\\\*[^/])+)\\\"},{\\\"begin\\\":\\\"((@)(?:default(?:value)?|license|version))\\\\\\\\s+(([''\\\\\\\"]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.jsdoc\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.jsdoc\\\"}},\\\"contentName\\\":\\\"variable.other.jsdoc\\\",\\\"end\\\":\\\"(\\\\\\\\3)|(?=$|\\\\\\\\*/)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.jsdoc\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.jsdoc\\\"}}},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.jsdoc\\\"}},\\\"match\\\":\\\"((@)(?:default(?:value)?|license|tutorial|variation|version))\\\\\\\\s+([^\\\\\\\\s*]+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"}},\\\"match\\\":\\\"(@)(?:abstract|access|alias|api|arg|argument|async|attribute|augments|author|beta|borrows|bubbles|callback|chainable|class|classdesc|code|config|const|constant|constructor|constructs|copyright|default|defaultvalue|define|deprecated|desc|description|dict|emits|enum|event|example|exception|exports?|extends|extension(?:_?for)?|external|externs|file|fileoverview|final|fires|for|func|function|generator|global|hideconstructor|host|ignore|implements|implicitCast|inherit[Dd]oc|inner|instance|interface|internal|kind|lends|license|listens|main|member|memberof!?|method|mixes|mixins?|modifies|module|name|namespace|noalias|nocollapse|nocompile|nosideeffects|override|overview|package|param|polymer(?:Behavior)?|preserve|private|prop|property|protected|public|read[Oo]nly|record|require[ds]|returns?|see|since|static|struct|submodule|summary|suppress|template|this|throws|todo|tutorial|type|typedef|unrestricted|uses|var|variation|version|virtual|writeOnce|yields?)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},{\\\"include\\\":\\\"#inline-tags\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.block.tag.jsdoc\\\"}},\\\"match\\\":\\\"((@)(?:[_$[:alpha:]][_$[:alnum:]]*))(?=\\\\\\\\s+)\\\"}]},\\\"enum-declaration\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?(?:\\\\\\\\b(const)\\\\\\\\s+)?\\\\\\\\b(enum)\\\\\\\\s+([_$[:alpha:]][_$[:alnum:]]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.enum.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.type.enum.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})\\\",\\\"name\\\":\\\"meta.enum.declaration.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.enummember.js.jsx\\\"}},\\\"end\\\":\\\"(?=,|\\\\\\\\}|$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#variable-initializer\\\"}]},{\\\"begin\\\":\\\"(?=((\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`)|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])+\\\\\\\\])))\\\",\\\"end\\\":\\\"(?=,|\\\\\\\\}|$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#array-literal\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#variable-initializer\\\"}]},{\\\"include\\\":\\\"#punctuation-comma\\\"}]}]},\\\"export-declaration\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.as.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.namespace.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.module.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(export)\\\\\\\\s+(as)\\\\\\\\s+(namespace)\\\\\\\\s+([_$[:alpha:]][_$[:alnum:]]*)\\\"},{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(export)(?:\\\\\\\\s+(type))?(?:(?:\\\\\\\\s*(=))|(?:\\\\\\\\s+(default)(?=\\\\\\\\s+)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.type.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.assignment.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.control.default.js.jsx\\\"}},\\\"end\\\":\\\"(?=$|;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.export.default.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interface-declaration\\\"},{\\\"include\\\":\\\"#expression\\\"}]},{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(export)(?:\\\\\\\\s+(type))?\\\\\\\\b(?!(\\\\\\\\$)|(\\\\\\\\s*:))((?=\\\\\\\\s*[\\\\\\\\{*])|((?=\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*(\\\\\\\\s|,))(?!\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.type.js.jsx\\\"}},\\\"end\\\":\\\"(?=$|;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.export.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#import-export-declaration\\\"}]}]},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#expressionWithoutIdentifiers\\\"},{\\\"include\\\":\\\"#identifiers\\\"},{\\\"include\\\":\\\"#expressionPunctuations\\\"}]},\\\"expression-inside-possibly-arrow-parens\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#expressionWithoutIdentifiers\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#destructuring-parameter\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(override|public|protected|private|readonly)\\\\\\\\s+(?=(override|public|protected|private|readonly)\\\\\\\\s+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.js.jsx variable.language.this.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.function.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.optional.js.jsx\\\"}},\\\"match\\\":\\\"(?:(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(override|public|private|protected|readonly)\\\\\\\\s+)?(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?(?<!=|:)(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(this)|([_$[:alpha:]][_$[:alnum:]]*))(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\\\\\\s*(\\\\\\\\??)(?=\\\\\\\\s*(=\\\\\\\\s*(((async\\\\\\\\s+)?((function\\\\\\\\s*[(<*])|(function\\\\\\\\s+)|([_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*=>)))|((async\\\\\\\\s*)?(((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*))))))|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?[(]\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([)]\\\\\\\\s*:)|((\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*)?[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*:)))|([<]\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s+extends\\\\\\\\s*[^=>])|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?\\\\\\\\(\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([_$[:alpha:]]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*[_$[:alpha:]]))([^()\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))*)?\\\\\\\\)(\\\\\\\\s*:\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+)?\\\\\\\\s*=>)))))|(:\\\\\\\\s*((<)|([(]\\\\\\\\s*(([)])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.)|([_$[:alnum:]]+\\\\\\\\s*(([:,?=])|([)]\\\\\\\\s*=>)))))))|(:\\\\\\\\s*(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))Function(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))|(:\\\\\\\\s*((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))))))|(:\\\\\\\\s*(=>|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(<[^<>]*>)|[^<>(),=])+=\\\\\\\\s*(((async\\\\\\\\s+)?((function\\\\\\\\s*[(<*])|(function\\\\\\\\s+)|([_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*=>)))|((async\\\\\\\\s*)?(((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*))))))|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?[(]\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([)]\\\\\\\\s*:)|((\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*)?[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*:)))|([<]\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s+extends\\\\\\\\s*[^=>])|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?\\\\\\\\(\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([_$[:alpha:]]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*[_$[:alpha:]]))([^()\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))*)?\\\\\\\\)(\\\\\\\\s*:\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+)?\\\\\\\\s*=>))))))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.js.jsx variable.language.this.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.parameter.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.optional.js.jsx\\\"}},\\\"match\\\":\\\"(?:(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(override|public|private|protected|readonly)\\\\\\\\s+)?(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?(?<!=|:)(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(this)|([_$[:alpha:]][_$[:alnum:]]*))(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\\\\\\s*(\\\\\\\\??)(?=\\\\\\\\s*[:,]|$)\\\"},{\\\"include\\\":\\\"#type-annotation\\\"},{\\\"include\\\":\\\"#variable-initializer\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.parameter.js.jsx\\\"},{\\\"include\\\":\\\"#identifiers\\\"},{\\\"include\\\":\\\"#expressionPunctuations\\\"}]},\\\"expression-operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(await)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.control.flow.js.jsx\\\"},{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(yield)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))(?=\\\\\\\\s*\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*\\\\\\\\*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\*\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.generator.asterisk.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.generator.asterisk.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(yield)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))(?:\\\\\\\\s*(\\\\\\\\*))?\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))delete(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.operator.expression.delete.js.jsx\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))in(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))(?!\\\\\\\\()\\\",\\\"name\\\":\\\"keyword.operator.expression.in.js.jsx\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))of(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))(?!\\\\\\\\()\\\",\\\"name\\\":\\\"keyword.operator.expression.of.js.jsx\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))instanceof(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.operator.expression.instanceof.js.jsx\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))new(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.operator.new.js.jsx\\\"},{\\\"include\\\":\\\"#typeof-operator\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))void(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.operator.expression.void.js.jsx\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.as.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(as)\\\\\\\\s+(const)(?=\\\\\\\\s*($|[;,:})\\\\\\\\]]))\\\"},{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(as)|(satisfies))\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.as.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.satisfies.js.jsx\\\"}},\\\"end\\\":\\\"(?=^|[;),}\\\\\\\\]:?\\\\\\\\-\\\\\\\\+\\\\\\\\>]|\\\\\\\\|\\\\\\\\||\\\\\\\\&\\\\\\\\&|\\\\\\\\!\\\\\\\\=\\\\\\\\=|$|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(as|satisfies)\\\\\\\\s+)|(\\\\\\\\s+\\\\\\\\<))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},{\\\"match\\\":\\\"\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\",\\\"name\\\":\\\"keyword.operator.spread.js.jsx\\\"},{\\\"match\\\":\\\"\\\\\\\\*=|(?<!\\\\\\\\()/=|%=|\\\\\\\\+=|\\\\\\\\-=\\\",\\\"name\\\":\\\"keyword.operator.assignment.compound.js.jsx\\\"},{\\\"match\\\":\\\"\\\\\\\\&=|\\\\\\\\^=|<<=|>>=|>>>=|\\\\\\\\|=\\\",\\\"name\\\":\\\"keyword.operator.assignment.compound.bitwise.js.jsx\\\"},{\\\"match\\\":\\\"<<|>>>|>>\\\",\\\"name\\\":\\\"keyword.operator.bitwise.shift.js.jsx\\\"},{\\\"match\\\":\\\"===|!==|==|!=\\\",\\\"name\\\":\\\"keyword.operator.comparison.js.jsx\\\"},{\\\"match\\\":\\\"<=|>=|<>|<|>\\\",\\\"name\\\":\\\"keyword.operator.relational.js.jsx\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logical.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.compound.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.js.jsx\\\"}},\\\"match\\\":\\\"(?<=[_$[:alnum:]])(\\\\\\\\!)\\\\\\\\s*(?:(/=)|(?:(/)(?![/*])))\\\"},{\\\"match\\\":\\\"\\\\\\\\!|&&|\\\\\\\\|\\\\\\\\||\\\\\\\\?\\\\\\\\?\\\",\\\"name\\\":\\\"keyword.operator.logical.js.jsx\\\"},{\\\"match\\\":\\\"\\\\\\\\&|~|\\\\\\\\^|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.bitwise.js.jsx\\\"},{\\\"match\\\":\\\"\\\\\\\\=\\\",\\\"name\\\":\\\"keyword.operator.assignment.js.jsx\\\"},{\\\"match\\\":\\\"--\\\",\\\"name\\\":\\\"keyword.operator.decrement.js.jsx\\\"},{\\\"match\\\":\\\"\\\\\\\\+\\\\\\\\+\\\",\\\"name\\\":\\\"keyword.operator.increment.js.jsx\\\"},{\\\"match\\\":\\\"%|\\\\\\\\*|/|-|\\\\\\\\+\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.js.jsx\\\"},{\\\"begin\\\":\\\"(?<=[_$[:alnum:])\\\\\\\\]])\\\\\\\\s*(?=(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)+(?:(/=)|(?:(/)(?![/*]))))\\\",\\\"end\\\":\\\"(?:(/=)|(?:(/)(?!\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/)))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.assignment.compound.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.assignment.compound.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.js.jsx\\\"}},\\\"match\\\":\\\"(?<=[_$[:alnum:])\\\\\\\\]])\\\\\\\\s*(?:(/=)|(?:(/)(?![/*])))\\\"}]},\\\"expressionPunctuations\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#punctuation-comma\\\"},{\\\"include\\\":\\\"#punctuation-accessor\\\"}]},\\\"expressionWithoutIdentifiers\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#jsx\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#regex\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#function-expression\\\"},{\\\"include\\\":\\\"#class-expression\\\"},{\\\"include\\\":\\\"#arrow-function\\\"},{\\\"include\\\":\\\"#paren-expression-possibly-arrow\\\"},{\\\"include\\\":\\\"#cast\\\"},{\\\"include\\\":\\\"#ternary-expression\\\"},{\\\"include\\\":\\\"#new-expr\\\"},{\\\"include\\\":\\\"#instanceof-expr\\\"},{\\\"include\\\":\\\"#object-literal\\\"},{\\\"include\\\":\\\"#expression-operators\\\"},{\\\"include\\\":\\\"#function-call\\\"},{\\\"include\\\":\\\"#literal\\\"},{\\\"include\\\":\\\"#support-objects\\\"},{\\\"include\\\":\\\"#paren-expression\\\"}]},\\\"field-declaration\\\":{\\\"begin\\\":\\\"(?<!\\\\\\\\()(?:(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(readonly)\\\\\\\\s+)?(?=\\\\\\\\s*((\\\\\\\\b(?<!\\\\\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:b|B)[01][01_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|((?<!\\\\\\\\$)(?:(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)(n)?\\\\\\\\B)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(n)?\\\\\\\\b(?!\\\\\\\\.)))(?!\\\\\\\\$))|(\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*)|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`)|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])+\\\\\\\\]))\\\\\\\\s*(?:(?:(\\\\\\\\?)|(\\\\\\\\!))\\\\\\\\s*)?(=|:|;|,|\\\\\\\\}|$))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\}|;|,|$|(^(?!\\\\\\\\s*((\\\\\\\\b(?<!\\\\\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:b|B)[01][01_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|((?<!\\\\\\\\$)(?:(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)(n)?\\\\\\\\B)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(n)?\\\\\\\\b(?!\\\\\\\\.)))(?!\\\\\\\\$))|(\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*)|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`)|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])+\\\\\\\\]))\\\\\\\\s*(?:(?:(\\\\\\\\?)|(\\\\\\\\!))\\\\\\\\s*)?(=|:|;|,|$))))|(?<=\\\\\\\\})\\\",\\\"name\\\":\\\"meta.field.declaration.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable-initializer\\\"},{\\\"include\\\":\\\"#type-annotation\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#array-literal\\\"},{\\\"include\\\":\\\"#numeric-literal\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.definition.property.js.jsx entity.name.function.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.optional.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.definiteassignment.js.jsx\\\"}},\\\"match\\\":\\\"(\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*)(?:(\\\\\\\\?)|(\\\\\\\\!))?(?=\\\\\\\\s*\\\\\\\\s*(=\\\\\\\\s*(((async\\\\\\\\s+)?((function\\\\\\\\s*[(<*])|(function\\\\\\\\s+)|([_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*=>)))|((async\\\\\\\\s*)?(((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*))))))|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?[(]\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([)]\\\\\\\\s*:)|((\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*)?[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*:)))|([<]\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s+extends\\\\\\\\s*[^=>])|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?\\\\\\\\(\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([_$[:alpha:]]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*[_$[:alpha:]]))([^()\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))*)?\\\\\\\\)(\\\\\\\\s*:\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+)?\\\\\\\\s*=>)))))|(:\\\\\\\\s*((<)|([(]\\\\\\\\s*(([)])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.)|([_$[:alnum:]]+\\\\\\\\s*(([:,?=])|([)]\\\\\\\\s*=>)))))))|(:\\\\\\\\s*(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))Function(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))|(:\\\\\\\\s*((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))))))|(:\\\\\\\\s*(=>|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(<[^<>]*>)|[^<>(),=])+=\\\\\\\\s*(((async\\\\\\\\s+)?((function\\\\\\\\s*[(<*])|(function\\\\\\\\s+)|([_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*=>)))|((async\\\\\\\\s*)?(((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*))))))|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?[(]\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([)]\\\\\\\\s*:)|((\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*)?[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*:)))|([<]\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s+extends\\\\\\\\s*[^=>])|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?\\\\\\\\(\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([_$[:alpha:]]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*[_$[:alpha:]]))([^()\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))*)?\\\\\\\\)(\\\\\\\\s*:\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+)?\\\\\\\\s*=>))))))\\\"},{\\\"match\\\":\\\"\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*\\\",\\\"name\\\":\\\"meta.definition.property.js.jsx variable.object.property.js.jsx\\\"},{\\\"match\\\":\\\"\\\\\\\\?\\\",\\\"name\\\":\\\"keyword.operator.optional.js.jsx\\\"},{\\\"match\\\":\\\"\\\\\\\\!\\\",\\\"name\\\":\\\"keyword.operator.definiteassignment.js.jsx\\\"}]},\\\"for-loop\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))for(?=((\\\\\\\\s+|(\\\\\\\\s*\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*))await)?\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)?(\\\\\\\\())\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.loop.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"match\\\":\\\"await\\\",\\\"name\\\":\\\"keyword.control.loop.js.jsx\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#var-expr\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-semicolon\\\"}]}]},\\\"function-body\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"include\\\":\\\"#function-parameters\\\"},{\\\"include\\\":\\\"#return-type\\\"},{\\\"include\\\":\\\"#type-function-return-type\\\"},{\\\"include\\\":\\\"#decl-block\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"keyword.generator.asterisk.js.jsx\\\"}]},\\\"function-call\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(((([_$[:alpha:]][_$[:alnum:]]*)(\\\\\\\\s*\\\\\\\\??\\\\\\\\.\\\\\\\\s*(\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*))*)|(\\\\\\\\??\\\\\\\\.\\\\\\\\s*\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*))|(?<=[\\\\\\\\)]))\\\\\\\\s*(?:(\\\\\\\\?\\\\\\\\.\\\\\\\\s*)|(\\\\\\\\!))?((<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>|\\\\\\\\<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))(([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>|\\\\\\\\<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>)*(?<!=)\\\\\\\\>))*(?<!=)\\\\\\\\>)*(?<!=)>\\\\\\\\s*)?\\\\\\\\())\\\",\\\"end\\\":\\\"(?<=\\\\\\\\))(?!(((([_$[:alpha:]][_$[:alnum:]]*)(\\\\\\\\s*\\\\\\\\??\\\\\\\\.\\\\\\\\s*(\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*))*)|(\\\\\\\\??\\\\\\\\.\\\\\\\\s*\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*))|(?<=[\\\\\\\\)]))\\\\\\\\s*(?:(\\\\\\\\?\\\\\\\\.\\\\\\\\s*)|(\\\\\\\\!))?((<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>|\\\\\\\\<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))(([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>|\\\\\\\\<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>)*(?<!=)\\\\\\\\>))*(?<!=)\\\\\\\\>)*(?<!=)>\\\\\\\\s*)?\\\\\\\\())\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(([_$[:alpha:]][_$[:alnum:]]*)(\\\\\\\\s*\\\\\\\\??\\\\\\\\.\\\\\\\\s*(\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*))*)|(\\\\\\\\??\\\\\\\\.\\\\\\\\s*\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*))\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(?:(\\\\\\\\?\\\\\\\\.\\\\\\\\s*)|(\\\\\\\\!))?((<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>|\\\\\\\\<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))(([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>|\\\\\\\\<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>)*(?<!=)\\\\\\\\>))*(?<!=)\\\\\\\\>)*(?<!=)>\\\\\\\\s*)?\\\\\\\\())\\\",\\\"name\\\":\\\"meta.function-call.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-target\\\"}]},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#function-call-optionals\\\"},{\\\"include\\\":\\\"#type-arguments\\\"},{\\\"include\\\":\\\"#paren-expression\\\"}]},{\\\"begin\\\":\\\"(?=(((([_$[:alpha:]][_$[:alnum:]]*)(\\\\\\\\s*\\\\\\\\??\\\\\\\\.\\\\\\\\s*(\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*))*)|(\\\\\\\\??\\\\\\\\.\\\\\\\\s*\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*))|(?<=[\\\\\\\\)]))(<\\\\\\\\s*[\\\\\\\\{\\\\\\\\[\\\\\\\\(]\\\\\\\\s*$))\\\",\\\"end\\\":\\\"(?<=\\\\\\\\>)(?!(((([_$[:alpha:]][_$[:alnum:]]*)(\\\\\\\\s*\\\\\\\\??\\\\\\\\.\\\\\\\\s*(\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*))*)|(\\\\\\\\??\\\\\\\\.\\\\\\\\s*\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*))|(?<=[\\\\\\\\)]))(<\\\\\\\\s*[\\\\\\\\{\\\\\\\\[\\\\\\\\(]\\\\\\\\s*$))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(([_$[:alpha:]][_$[:alnum:]]*)(\\\\\\\\s*\\\\\\\\??\\\\\\\\.\\\\\\\\s*(\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*))*)|(\\\\\\\\??\\\\\\\\.\\\\\\\\s*\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*))\\\",\\\"end\\\":\\\"(?=(<\\\\\\\\s*[\\\\\\\\{\\\\\\\\[\\\\\\\\(]\\\\\\\\s*$))\\\",\\\"name\\\":\\\"meta.function-call.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-target\\\"}]},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#function-call-optionals\\\"},{\\\"include\\\":\\\"#type-arguments\\\"}]}]},\\\"function-call-optionals\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\?\\\\\\\\.\\\",\\\"name\\\":\\\"meta.function-call.js.jsx punctuation.accessor.optional.js.jsx\\\"},{\\\"match\\\":\\\"\\\\\\\\!\\\",\\\"name\\\":\\\"meta.function-call.js.jsx keyword.operator.definiteassignment.js.jsx\\\"}]},\\\"function-call-target\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#support-function-call-identifiers\\\"},{\\\"match\\\":\\\"(\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*)\\\",\\\"name\\\":\\\"entity.name.function.js.jsx\\\"}]},\\\"function-declaration\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?(?:(async)\\\\\\\\s+)?(function\\\\\\\\b)(?:\\\\\\\\s*(\\\\\\\\*))?(?:(?:\\\\\\\\s+|(?<=\\\\\\\\*))([_$[:alpha:]][_$[:alnum:]]*))?\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.function.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.generator.asterisk.js.jsx\\\"},\\\"6\\\":{\\\"name\\\":\\\"meta.definition.function.js.jsx entity.name.function.js.jsx\\\"}},\\\"end\\\":\\\"(?=;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b))|(?<=\\\\\\\\})\\\",\\\"name\\\":\\\"meta.function.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-name\\\"},{\\\"include\\\":\\\"#function-body\\\"}]},\\\"function-expression\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(async)\\\\\\\\s+)?(function\\\\\\\\b)(?:\\\\\\\\s*(\\\\\\\\*))?(?:(?:\\\\\\\\s+|(?<=\\\\\\\\*))([_$[:alpha:]][_$[:alnum:]]*))?\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.function.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.generator.asterisk.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.definition.function.js.jsx entity.name.function.js.jsx\\\"}},\\\"end\\\":\\\"(?=;)|(?<=\\\\\\\\})\\\",\\\"name\\\":\\\"meta.function.expression.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-name\\\"},{\\\"include\\\":\\\"#single-line-comment-consuming-line-ending\\\"},{\\\"include\\\":\\\"#function-body\\\"}]},\\\"function-name\\\":{\\\"match\\\":\\\"[_$[:alpha:]][_$[:alnum:]]*\\\",\\\"name\\\":\\\"meta.definition.function.js.jsx entity.name.function.js.jsx\\\"},\\\"function-parameters\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.js.jsx\\\"}},\\\"name\\\":\\\"meta.parameters.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-parameters-body\\\"}]},\\\"function-parameters-body\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#destructuring-parameter\\\"},{\\\"include\\\":\\\"#parameter-name\\\"},{\\\"include\\\":\\\"#parameter-type-annotation\\\"},{\\\"include\\\":\\\"#variable-initializer\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.parameter.js.jsx\\\"}]},\\\"identifiers\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#object-identifiers\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.accessor.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.optional.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.js.jsx\\\"}},\\\"match\\\":\\\"(?:(?:(\\\\\\\\.)|(\\\\\\\\?\\\\\\\\.(?!\\\\\\\\s*[[:digit:]])))\\\\\\\\s*)?([_$[:alpha:]][_$[:alnum:]]*)(?=\\\\\\\\s*=\\\\\\\\s*(((async\\\\\\\\s+)?((function\\\\\\\\s*[(<*])|(function\\\\\\\\s+)|([_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*=>)))|((async\\\\\\\\s*)?(((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*))))))|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?[(]\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([)]\\\\\\\\s*:)|((\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*)?[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*:)))|([<]\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s+extends\\\\\\\\s*[^=>])|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?\\\\\\\\(\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([_$[:alpha:]]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*[_$[:alpha:]]))([^()\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))*)?\\\\\\\\)(\\\\\\\\s*:\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+)?\\\\\\\\s*=>)))))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.accessor.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.optional.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.constant.property.js.jsx\\\"}},\\\"match\\\":\\\"(?:(\\\\\\\\.)|(\\\\\\\\?\\\\\\\\.(?!\\\\\\\\s*[[:digit:]])))\\\\\\\\s*(\\\\\\\\#?[[:upper:]][_$[:digit:][:upper:]]*)(?![_$[:alnum:]])\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.accessor.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.optional.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.property.js.jsx\\\"}},\\\"match\\\":\\\"(?:(\\\\\\\\.)|(\\\\\\\\?\\\\\\\\.(?!\\\\\\\\s*[[:digit:]])))\\\\\\\\s*(\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*)\\\"},{\\\"match\\\":\\\"([[:upper:]][_$[:digit:][:upper:]]*)(?![_$[:alnum:]])\\\",\\\"name\\\":\\\"variable.other.constant.js.jsx\\\"},{\\\"match\\\":\\\"[_$[:alpha:]][_$[:alnum:]]*\\\",\\\"name\\\":\\\"variable.other.readwrite.js.jsx\\\"}]},\\\"if-statement\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?=\\\\\\\\bif\\\\\\\\s*(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))\\\\\\\\s*(?!\\\\\\\\{))\\\",\\\"end\\\":\\\"(?=;|$|\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(if)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.conditional.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\))\\\\\\\\s*\\\\\\\\/(?![\\\\\\\\/*])(?=(?:[^\\\\\\\\/\\\\\\\\\\\\\\\\\\\\\\\\[]|\\\\\\\\\\\\\\\\.|\\\\\\\\[([^\\\\\\\\]\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\])+\\\\\\\\/([dgimsuvy]+|(?![\\\\\\\\/\\\\\\\\*])|(?=\\\\\\\\/\\\\\\\\*))(?!\\\\\\\\s*[a-zA-Z0-9_$]))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.js.jsx\\\"}},\\\"end\\\":\\\"(/)([dgimsuvy]*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.js.jsx\\\"}},\\\"name\\\":\\\"string.regexp.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp\\\"}]},{\\\"include\\\":\\\"#statements\\\"}]}]},\\\"import-declaration\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b(import)(?:\\\\\\\\s+(type)(?!\\\\\\\\s+from))?(?!\\\\\\\\s*[:\\\\\\\\(])(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.import.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.control.type.js.jsx\\\"}},\\\"end\\\":\\\"(?<!^import|[^\\\\\\\\._$[:alnum:]]import)(?=;|$|^)\\\",\\\"name\\\":\\\"meta.import.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#single-line-comment-consuming-line-ending\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"begin\\\":\\\"(?<=^import|[^\\\\\\\\._$[:alnum:]]import)(?!\\\\\\\\s*[\\\\\\\"'])\\\",\\\"end\\\":\\\"\\\\\\\\bfrom\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.from.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#import-export-declaration\\\"}]},{\\\"include\\\":\\\"#import-export-declaration\\\"}]},\\\"import-equals-declaration\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b(import)(?:\\\\\\\\s+(type))?\\\\\\\\s+([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(=)\\\\\\\\s*(require)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.import.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.control.type.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"variable.other.readwrite.alias.js.jsx\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.assignment.js.jsx\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.control.require.js.jsx\\\"},\\\"8\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"name\\\":\\\"meta.import-equals.external.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#string\\\"}]},{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b(import)(?:\\\\\\\\s+(type))?\\\\\\\\s+([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(=)\\\\\\\\s*(?!require\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.import.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.control.type.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"variable.other.readwrite.alias.js.jsx\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.assignment.js.jsx\\\"}},\\\"end\\\":\\\"(?=;|$|^)\\\",\\\"name\\\":\\\"meta.import-equals.internal.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#single-line-comment-consuming-line-ending\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.module.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.accessor.optional.js.jsx\\\"}},\\\"match\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(?:(\\\\\\\\.)|(\\\\\\\\?\\\\\\\\.(?!\\\\\\\\s*[[:digit:]])))\\\"},{\\\"match\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\",\\\"name\\\":\\\"variable.other.readwrite.js.jsx\\\"}]}]},\\\"import-export-assert-clause\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(with)|(assert))\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.with.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.assert.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"match\\\":\\\"(?:[_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(?=(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*:)\\\",\\\"name\\\":\\\"meta.object-literal.key.js.jsx\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.key-value.js.jsx\\\"}]},\\\"import-export-block\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"name\\\":\\\"meta.block.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#import-export-clause\\\"}]},\\\"import-export-clause\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.type.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.default.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.language.import-export-all.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.readwrite.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"string.quoted.alias.js.jsx\\\"},\\\"12\\\":{\\\"name\\\":\\\"keyword.control.as.js.jsx\\\"},\\\"13\\\":{\\\"name\\\":\\\"keyword.control.default.js.jsx\\\"},\\\"14\\\":{\\\"name\\\":\\\"variable.other.readwrite.alias.js.jsx\\\"},\\\"15\\\":{\\\"name\\\":\\\"string.quoted.alias.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(?:(\\\\\\\\btype)\\\\\\\\s+)?(?:(\\\\\\\\bdefault)|(\\\\\\\\*)|(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*)|((\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))))\\\\\\\\s+(as)\\\\\\\\s+(?:(default(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))|([_$[:alpha:]][_$[:alnum:]]*)|((\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`)))\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"constant.language.import-export-all.js.jsx\\\"},{\\\"match\\\":\\\"\\\\\\\\b(default)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.default.js.jsx\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.type.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.readwrite.alias.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"string.quoted.alias.js.jsx\\\"}},\\\"match\\\":\\\"(?:(\\\\\\\\btype)\\\\\\\\s+)?(?:([_$[:alpha:]][_$[:alnum:]]*)|((\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`)))\\\"}]},\\\"import-export-declaration\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#import-export-block\\\"},{\\\"match\\\":\\\"\\\\\\\\bfrom\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.from.js.jsx\\\"},{\\\"include\\\":\\\"#import-export-assert-clause\\\"},{\\\"include\\\":\\\"#import-export-clause\\\"}]},\\\"indexer-declaration\\\":{\\\"begin\\\":\\\"(?:(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(readonly)\\\\\\\\s*)?\\\\\\\\s*(\\\\\\\\[)\\\\\\\\s*([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(?=:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.brace.square.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.js.jsx\\\"}},\\\"end\\\":\\\"(\\\\\\\\])\\\\\\\\s*(\\\\\\\\?\\\\\\\\s*)?|$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.brace.square.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.optional.js.jsx\\\"}},\\\"name\\\":\\\"meta.indexer.declaration.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-annotation\\\"}]},\\\"indexer-mapped-type-declaration\\\":{\\\"begin\\\":\\\"(?:(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))([+-])?(readonly)\\\\\\\\s*)?\\\\\\\\s*(\\\\\\\\[)\\\\\\\\s*([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s+(in)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.type.modifier.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"meta.brace.square.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.expression.in.js.jsx\\\"}},\\\"end\\\":\\\"(\\\\\\\\])([+-])?\\\\\\\\s*(\\\\\\\\?\\\\\\\\s*)?|$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.brace.square.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.type.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.optional.js.jsx\\\"}},\\\"name\\\":\\\"meta.indexer.mappedtype.declaration.js.jsx\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.as.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(as)\\\\\\\\s+\\\"},{\\\"include\\\":\\\"#type\\\"}]},\\\"inline-tags\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.bracket.square.begin.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.bracket.square.end.jsdoc\\\"}},\\\"match\\\":\\\"(\\\\\\\\[)[^\\\\\\\\]]+(\\\\\\\\])(?={@(?:link|linkcode|linkplain|tutorial))\\\",\\\"name\\\":\\\"constant.other.description.jsdoc\\\"},{\\\"begin\\\":\\\"({)((@)(?:link(?:code|plain)?|tutorial))\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.bracket.curly.begin.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.class.jsdoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.inline.tag.jsdoc\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\*/)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.bracket.curly.end.jsdoc\\\"}},\\\"name\\\":\\\"entity.name.type.instance.jsdoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.link.underline.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.pipe.jsdoc\\\"}},\\\"match\\\":\\\"\\\\\\\\G((?=https?://)(?:[^|}\\\\\\\\s*]|\\\\\\\\*[/])+)(\\\\\\\\|)?\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.description.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.pipe.jsdoc\\\"}},\\\"match\\\":\\\"\\\\\\\\G((?:[^{}@\\\\\\\\s|*]|\\\\\\\\*[^/])+)(\\\\\\\\|)?\\\"}]}]},\\\"instanceof-expr\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(instanceof)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.expression.instanceof.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\))|(?=[;),}\\\\\\\\]:?\\\\\\\\-\\\\\\\\+\\\\\\\\>]|\\\\\\\\|\\\\\\\\||\\\\\\\\&\\\\\\\\&|\\\\\\\\!\\\\\\\\=\\\\\\\\=|$|(===|!==|==|!=)|(([\\\\\\\\&\\\\\\\\~\\\\\\\\^\\\\\\\\|]\\\\\\\\s*)?[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s+instanceof(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))function((\\\\\\\\s+[_$[:alpha:]][_$[:alnum:]]*)|(\\\\\\\\s*[\\\\\\\\(]))))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},\\\"interface-declaration\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b(?:(abstract)\\\\\\\\s+)?\\\\\\\\b(interface)\\\\\\\\b(?=\\\\\\\\s+|/[/*])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.interface.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})\\\",\\\"name\\\":\\\"meta.interface.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#class-or-interface-heritage\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.type.interface.js.jsx\\\"}},\\\"match\\\":\\\"[_$[:alpha:]][_$[:alnum:]]*\\\"},{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"include\\\":\\\"#class-or-interface-body\\\"}]},\\\"jsdoctype\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G({)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.type.instance.jsdoc\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.bracket.curly.begin.jsdoc\\\"}},\\\"contentName\\\":\\\"entity.name.type.instance.jsdoc\\\",\\\"end\\\":\\\"((}))\\\\\\\\s*|(?=\\\\\\\\*/)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.instance.jsdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.bracket.curly.end.jsdoc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#brackets\\\"}]}]},\\\"jsx\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#jsx-tag-without-attributes-in-expression\\\"},{\\\"include\\\":\\\"#jsx-tag-in-expression\\\"}]},\\\"jsx-children\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#jsx-tag-without-attributes\\\"},{\\\"include\\\":\\\"#jsx-tag\\\"},{\\\"include\\\":\\\"#jsx-evaluated-code\\\"},{\\\"include\\\":\\\"#jsx-entities\\\"}]},\\\"jsx-entities\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.entity.js.jsx\\\"}},\\\"match\\\":\\\"(&)([a-zA-Z0-9]+|#[0-9]+|#x[0-9a-fA-F]+)(;)\\\",\\\"name\\\":\\\"constant.character.entity.js.jsx\\\"}]},\\\"jsx-evaluated-code\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.begin.js.jsx\\\"}},\\\"contentName\\\":\\\"meta.embedded.expression.js.jsx\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.end.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"jsx-string-double-quoted\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.js.jsx\\\"}},\\\"name\\\":\\\"string.quoted.double.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#jsx-entities\\\"}]},\\\"jsx-string-single-quoted\\\":{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.js.jsx\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.js.jsx\\\"}},\\\"name\\\":\\\"string.quoted.single.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#jsx-entities\\\"}]},\\\"jsx-tag\\\":{\\\"begin\\\":\\\"(?=(<)\\\\\\\\s*(?:([_$[:alpha:]][-_$[:alnum:].]*)(?<!\\\\\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-_$[:alnum:].]*))(?<!\\\\\\\\.|-))(?=((<\\\\\\\\s*)|(\\\\\\\\s+))(?!\\\\\\\\?)|\\\\\\\\/?>))\\\",\\\"end\\\":\\\"(/>)|(?:(</)\\\\\\\\s*(?:([_$[:alpha:]][-_$[:alnum:].]*)(?<!\\\\\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-_$[:alnum:].]*))(?<!\\\\\\\\.|-))?\\\\\\\\s*(>))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag.end.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.tag.begin.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.tag.namespace.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.tag.js.jsx\\\"},\\\"6\\\":{\\\"name\\\":\\\"support.class.component.js.jsx\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.tag.end.js.jsx\\\"}},\\\"name\\\":\\\"meta.tag.js.jsx\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(<)\\\\\\\\s*(?:([_$[:alpha:]][-_$[:alnum:].]*)(?<!\\\\\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-_$[:alnum:].]*))(?<!\\\\\\\\.|-))(?=((<\\\\\\\\s*)|(\\\\\\\\s+))(?!\\\\\\\\?)|\\\\\\\\/?>)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag.begin.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.namespace.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.tag.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"support.class.component.js.jsx\\\"}},\\\"end\\\":\\\"(?=[/]?>)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#type-arguments\\\"},{\\\"include\\\":\\\"#jsx-tag-attributes\\\"}]},{\\\"begin\\\":\\\"(>)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag.end.js.jsx\\\"}},\\\"contentName\\\":\\\"meta.jsx.children.js.jsx\\\",\\\"end\\\":\\\"(?=</)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#jsx-children\\\"}]}]},\\\"jsx-tag-attribute-assignment\\\":{\\\"match\\\":\\\"=(?=\\\\\\\\s*(?:'|\\\\\\\"|{|/\\\\\\\\*|//|\\\\\\\\n))\\\",\\\"name\\\":\\\"keyword.operator.assignment.js.jsx\\\"},\\\"jsx-tag-attribute-name\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.attribute-name.namespace.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.other.attribute-name.js.jsx\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(?:([_$[:alpha:]][-_$[:alnum:].]*)(:))?([_$[:alpha:]][-_$[:alnum:]]*)(?=\\\\\\\\s|=|/?>|/\\\\\\\\*|//)\\\"},\\\"jsx-tag-attributes\\\":{\\\"begin\\\":\\\"\\\\\\\\s+\\\",\\\"end\\\":\\\"(?=[/]?>)\\\",\\\"name\\\":\\\"meta.tag.attributes.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#jsx-tag-attribute-name\\\"},{\\\"include\\\":\\\"#jsx-tag-attribute-assignment\\\"},{\\\"include\\\":\\\"#jsx-string-double-quoted\\\"},{\\\"include\\\":\\\"#jsx-string-single-quoted\\\"},{\\\"include\\\":\\\"#jsx-evaluated-code\\\"},{\\\"include\\\":\\\"#jsx-tag-attributes-illegal\\\"}]},\\\"jsx-tag-attributes-illegal\\\":{\\\"match\\\":\\\"\\\\\\\\S+\\\",\\\"name\\\":\\\"invalid.illegal.attribute.js.jsx\\\"},\\\"jsx-tag-in-expression\\\":{\\\"begin\\\":\\\"(?<!\\\\\\\\+\\\\\\\\+|--)(?<=[({\\\\\\\\[,?=>:*]|&&|\\\\\\\\|\\\\\\\\||\\\\\\\\?|\\\\\\\\*\\\\\\\\/|^await|[^\\\\\\\\._$[:alnum:]]await|^return|[^\\\\\\\\._$[:alnum:]]return|^default|[^\\\\\\\\._$[:alnum:]]default|^yield|[^\\\\\\\\._$[:alnum:]]yield|^)\\\\\\\\s*(?!<\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*((\\\\\\\\s+extends\\\\\\\\s+[^=>])|,))(?=(<)\\\\\\\\s*(?:([_$[:alpha:]][-_$[:alnum:].]*)(?<!\\\\\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-_$[:alnum:].]*))(?<!\\\\\\\\.|-))(?=((<\\\\\\\\s*)|(\\\\\\\\s+))(?!\\\\\\\\?)|\\\\\\\\/?>))\\\",\\\"end\\\":\\\"(?!(<)\\\\\\\\s*(?:([_$[:alpha:]][-_$[:alnum:].]*)(?<!\\\\\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-_$[:alnum:].]*))(?<!\\\\\\\\.|-))(?=((<\\\\\\\\s*)|(\\\\\\\\s+))(?!\\\\\\\\?)|\\\\\\\\/?>))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#jsx-tag\\\"}]},\\\"jsx-tag-without-attributes\\\":{\\\"begin\\\":\\\"(<)\\\\\\\\s*(?:([_$[:alpha:]][-_$[:alnum:].]*)(?<!\\\\\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-_$[:alnum:].]*))(?<!\\\\\\\\.|-))?\\\\\\\\s*(>)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag.begin.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.namespace.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.tag.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"support.class.component.js.jsx\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.tag.end.js.jsx\\\"}},\\\"contentName\\\":\\\"meta.jsx.children.js.jsx\\\",\\\"end\\\":\\\"(</)\\\\\\\\s*(?:([_$[:alpha:]][-_$[:alnum:].]*)(?<!\\\\\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-_$[:alnum:].]*))(?<!\\\\\\\\.|-))?\\\\\\\\s*(>)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag.begin.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.namespace.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.tag.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"support.class.component.js.jsx\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.tag.end.js.jsx\\\"}},\\\"name\\\":\\\"meta.tag.without-attributes.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#jsx-children\\\"}]},\\\"jsx-tag-without-attributes-in-expression\\\":{\\\"begin\\\":\\\"(?<!\\\\\\\\+\\\\\\\\+|--)(?<=[({\\\\\\\\[,?=>:*]|&&|\\\\\\\\|\\\\\\\\||\\\\\\\\?|\\\\\\\\*\\\\\\\\/|^await|[^\\\\\\\\._$[:alnum:]]await|^return|[^\\\\\\\\._$[:alnum:]]return|^default|[^\\\\\\\\._$[:alnum:]]default|^yield|[^\\\\\\\\._$[:alnum:]]yield|^)\\\\\\\\s*(?=(<)\\\\\\\\s*(?:([_$[:alpha:]][-_$[:alnum:].]*)(?<!\\\\\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-_$[:alnum:].]*))(?<!\\\\\\\\.|-))?\\\\\\\\s*(>))\\\",\\\"end\\\":\\\"(?!(<)\\\\\\\\s*(?:([_$[:alpha:]][-_$[:alnum:].]*)(?<!\\\\\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-_$[:alnum:].]*))(?<!\\\\\\\\.|-))?\\\\\\\\s*(>))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#jsx-tag-without-attributes\\\"}]},\\\"label\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(:)(?=\\\\\\\\s*\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.label.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.label.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#decl-block\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.label.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.label.js.jsx\\\"}},\\\"match\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(:)\\\"}]},\\\"literal\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#numeric-literal\\\"},{\\\"include\\\":\\\"#boolean-literal\\\"},{\\\"include\\\":\\\"#null-literal\\\"},{\\\"include\\\":\\\"#undefined-literal\\\"},{\\\"include\\\":\\\"#numericConstant-literal\\\"},{\\\"include\\\":\\\"#array-literal\\\"},{\\\"include\\\":\\\"#this-literal\\\"},{\\\"include\\\":\\\"#super-literal\\\"}]},\\\"method-declaration\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:\\\\\\\\b(override)\\\\\\\\s+)?(?:\\\\\\\\b(public|private|protected)\\\\\\\\s+)?(?:\\\\\\\\b(abstract)\\\\\\\\s+)?(?:\\\\\\\\b(async)\\\\\\\\s+)?\\\\\\\\s*\\\\\\\\b(constructor)\\\\\\\\b(?!:)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"storage.type.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\}|;|,|$)|(?<=\\\\\\\\})\\\",\\\"name\\\":\\\"meta.method.declaration.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#method-declaration-name\\\"},{\\\"include\\\":\\\"#function-body\\\"}]},{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:\\\\\\\\b(override)\\\\\\\\s+)?(?:\\\\\\\\b(public|private|protected)\\\\\\\\s+)?(?:\\\\\\\\b(abstract)\\\\\\\\s+)?(?:\\\\\\\\b(async)\\\\\\\\s+)?(?:(?:\\\\\\\\s*\\\\\\\\b(new)\\\\\\\\b(?!:)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))|(?:(\\\\\\\\*)\\\\\\\\s*)?)(?=\\\\\\\\s*((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*))?[\\\\\\\\(])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.new.js.jsx\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.generator.asterisk.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\}|;|,|$)|(?<=\\\\\\\\})\\\",\\\"name\\\":\\\"meta.method.declaration.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#method-declaration-name\\\"},{\\\"include\\\":\\\"#function-body\\\"}]},{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:\\\\\\\\b(override)\\\\\\\\s+)?(?:\\\\\\\\b(public|private|protected)\\\\\\\\s+)?(?:\\\\\\\\b(abstract)\\\\\\\\s+)?(?:\\\\\\\\b(async)\\\\\\\\s+)?(?:\\\\\\\\b(get|set)\\\\\\\\s+)?(?:(\\\\\\\\*)\\\\\\\\s*)?(?=\\\\\\\\s*(((\\\\\\\\b(?<!\\\\\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:b|B)[01][01_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|((?<!\\\\\\\\$)(?:(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)(n)?\\\\\\\\B)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(n)?\\\\\\\\b(?!\\\\\\\\.)))(?!\\\\\\\\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`)|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])+\\\\\\\\]))\\\\\\\\s*(\\\\\\\\??))\\\\\\\\s*((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*))?[\\\\\\\\(])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"storage.type.property.js.jsx\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.generator.asterisk.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\}|;|,|$)|(?<=\\\\\\\\})\\\",\\\"name\\\":\\\"meta.method.declaration.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#method-declaration-name\\\"},{\\\"include\\\":\\\"#function-body\\\"}]}]},\\\"method-declaration-name\\\":{\\\"begin\\\":\\\"(?=((\\\\\\\\b(?<!\\\\\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:b|B)[01][01_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|((?<!\\\\\\\\$)(?:(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)(n)?\\\\\\\\B)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(n)?\\\\\\\\b(?!\\\\\\\\.)))(?!\\\\\\\\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`)|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])+\\\\\\\\]))\\\\\\\\s*(\\\\\\\\??)\\\\\\\\s*[\\\\\\\\(\\\\\\\\<])\\\",\\\"end\\\":\\\"(?=\\\\\\\\(|\\\\\\\\<)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#array-literal\\\"},{\\\"include\\\":\\\"#numeric-literal\\\"},{\\\"match\\\":\\\"[_$[:alpha:]][_$[:alnum:]]*\\\",\\\"name\\\":\\\"meta.definition.method.js.jsx entity.name.function.js.jsx\\\"},{\\\"match\\\":\\\"\\\\\\\\?\\\",\\\"name\\\":\\\"keyword.operator.optional.js.jsx\\\"}]},\\\"namespace-declaration\\\":{\\\"begin\\\":\\\"(?:(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b(namespace|module)\\\\\\\\s+(?=[_$[:alpha:]\\\\\\\"'`]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.namespace.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})|(?=;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.namespace.declaration.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"match\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\",\\\"name\\\":\\\"entity.name.type.module.js.jsx\\\"},{\\\"include\\\":\\\"#punctuation-accessor\\\"},{\\\"include\\\":\\\"#decl-block\\\"}]},\\\"new-expr\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(new)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.new.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\))|(?=[;),}\\\\\\\\]:?\\\\\\\\-\\\\\\\\+\\\\\\\\>]|\\\\\\\\|\\\\\\\\||\\\\\\\\&\\\\\\\\&|\\\\\\\\!\\\\\\\\=\\\\\\\\=|$|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))new(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))function((\\\\\\\\s+[_$[:alpha:]][_$[:alnum:]]*)|(\\\\\\\\s*[\\\\\\\\(]))))\\\",\\\"name\\\":\\\"new.expr.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"null-literal\\\":{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))null(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"constant.language.null.js.jsx\\\"},\\\"numeric-literal\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.numeric.bigint.js.jsx\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\\\\\b(?!\\\\\\\\$)\\\",\\\"name\\\":\\\"constant.numeric.hex.js.jsx\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.numeric.bigint.js.jsx\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\$)0(?:b|B)[01][01_]*(n)?\\\\\\\\b(?!\\\\\\\\$)\\\",\\\"name\\\":\\\"constant.numeric.binary.js.jsx\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.numeric.bigint.js.jsx\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\\\\\b(?!\\\\\\\\$)\\\",\\\"name\\\":\\\"constant.numeric.octal.js.jsx\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.numeric.decimal.js.jsx\\\"},\\\"1\\\":{\\\"name\\\":\\\"meta.delimiter.decimal.period.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.numeric.bigint.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"meta.delimiter.decimal.period.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.numeric.bigint.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"meta.delimiter.decimal.period.js.jsx\\\"},\\\"6\\\":{\\\"name\\\":\\\"storage.type.numeric.bigint.js.jsx\\\"},\\\"7\\\":{\\\"name\\\":\\\"storage.type.numeric.bigint.js.jsx\\\"},\\\"8\\\":{\\\"name\\\":\\\"meta.delimiter.decimal.period.js.jsx\\\"},\\\"9\\\":{\\\"name\\\":\\\"storage.type.numeric.bigint.js.jsx\\\"},\\\"10\\\":{\\\"name\\\":\\\"meta.delimiter.decimal.period.js.jsx\\\"},\\\"11\\\":{\\\"name\\\":\\\"storage.type.numeric.bigint.js.jsx\\\"},\\\"12\\\":{\\\"name\\\":\\\"meta.delimiter.decimal.period.js.jsx\\\"},\\\"13\\\":{\\\"name\\\":\\\"storage.type.numeric.bigint.js.jsx\\\"},\\\"14\\\":{\\\"name\\\":\\\"storage.type.numeric.bigint.js.jsx\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\$)(?:(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)(n)?\\\\\\\\B)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(n)?\\\\\\\\b(?!\\\\\\\\.)))(?!\\\\\\\\$)\\\"}]},\\\"numericConstant-literal\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))NaN(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"constant.language.nan.js.jsx\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))Infinity(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"constant.language.infinity.js.jsx\\\"}]},\\\"object-binding-element\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?=((\\\\\\\\b(?<!\\\\\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:b|B)[01][01_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|((?<!\\\\\\\\$)(?:(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)(n)?\\\\\\\\B)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(n)?\\\\\\\\b(?!\\\\\\\\.)))(?!\\\\\\\\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`)|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])+\\\\\\\\]))\\\\\\\\s*(:))\\\",\\\"end\\\":\\\"(?=,|\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#object-binding-element-propertyName\\\"},{\\\"include\\\":\\\"#binding-element\\\"}]},{\\\"include\\\":\\\"#object-binding-pattern\\\"},{\\\"include\\\":\\\"#destructuring-variable-rest\\\"},{\\\"include\\\":\\\"#variable-initializer\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"object-binding-element-const\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?=((\\\\\\\\b(?<!\\\\\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:b|B)[01][01_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|((?<!\\\\\\\\$)(?:(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)(n)?\\\\\\\\B)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(n)?\\\\\\\\b(?!\\\\\\\\.)))(?!\\\\\\\\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`)|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])+\\\\\\\\]))\\\\\\\\s*(:))\\\",\\\"end\\\":\\\"(?=,|\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#object-binding-element-propertyName\\\"},{\\\"include\\\":\\\"#binding-element-const\\\"}]},{\\\"include\\\":\\\"#object-binding-pattern-const\\\"},{\\\"include\\\":\\\"#destructuring-variable-rest-const\\\"},{\\\"include\\\":\\\"#variable-initializer\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"object-binding-element-propertyName\\\":{\\\"begin\\\":\\\"(?=((\\\\\\\\b(?<!\\\\\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:b|B)[01][01_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|((?<!\\\\\\\\$)(?:(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)(n)?\\\\\\\\B)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(n)?\\\\\\\\b(?!\\\\\\\\.)))(?!\\\\\\\\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`)|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])+\\\\\\\\]))\\\\\\\\s*(:))\\\",\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.destructuring.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#array-literal\\\"},{\\\"include\\\":\\\"#numeric-literal\\\"},{\\\"match\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\",\\\"name\\\":\\\"variable.object.property.js.jsx\\\"}]},\\\"object-binding-pattern\\\":{\\\"begin\\\":\\\"(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.object.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.object.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#object-binding-element\\\"}]},\\\"object-binding-pattern-const\\\":{\\\"begin\\\":\\\"(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.object.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.object.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#object-binding-element-const\\\"}]},\\\"object-identifiers\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)(?=\\\\\\\\s*\\\\\\\\??\\\\\\\\.\\\\\\\\s*prototype\\\\\\\\b(?!\\\\\\\\$))\\\",\\\"name\\\":\\\"support.class.js.jsx\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.accessor.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.optional.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.constant.object.property.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.object.property.js.jsx\\\"}},\\\"match\\\":\\\"(?:(\\\\\\\\.)|(\\\\\\\\?\\\\\\\\.(?!\\\\\\\\s*[[:digit:]])))\\\\\\\\s*(?:(\\\\\\\\#?[[:upper:]][_$[:digit:][:upper:]]*)|(\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*))(?=\\\\\\\\s*\\\\\\\\??\\\\\\\\.\\\\\\\\s*\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.constant.object.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.object.js.jsx\\\"}},\\\"match\\\":\\\"(?:([[:upper:]][_$[:digit:][:upper:]]*)|([_$[:alpha:]][_$[:alnum:]]*))(?=\\\\\\\\s*\\\\\\\\??\\\\\\\\.\\\\\\\\s*\\\\\\\\#?[_$[:alpha:]][_$[:alnum:]]*)\\\"}]},\\\"object-literal\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"name\\\":\\\"meta.objectliteral.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#object-member\\\"}]},\\\"object-literal-method-declaration\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:\\\\\\\\b(async)\\\\\\\\s+)?(?:\\\\\\\\b(get|set)\\\\\\\\s+)?(?:(\\\\\\\\*)\\\\\\\\s*)?(?=\\\\\\\\s*(((\\\\\\\\b(?<!\\\\\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:b|B)[01][01_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|((?<!\\\\\\\\$)(?:(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)(n)?\\\\\\\\B)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(n)?\\\\\\\\b(?!\\\\\\\\.)))(?!\\\\\\\\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`)|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])+\\\\\\\\]))\\\\\\\\s*(\\\\\\\\??))\\\\\\\\s*((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*))?[\\\\\\\\(])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.property.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.generator.asterisk.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\}|;|,)|(?<=\\\\\\\\})\\\",\\\"name\\\":\\\"meta.method.declaration.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#method-declaration-name\\\"},{\\\"include\\\":\\\"#function-body\\\"},{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:\\\\\\\\b(async)\\\\\\\\s+)?(?:\\\\\\\\b(get|set)\\\\\\\\s+)?(?:(\\\\\\\\*)\\\\\\\\s*)?(?=\\\\\\\\s*(((\\\\\\\\b(?<!\\\\\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:b|B)[01][01_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|((?<!\\\\\\\\$)(?:(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)(n)?\\\\\\\\B)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(n)?\\\\\\\\b(?!\\\\\\\\.)))(?!\\\\\\\\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`)|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])+\\\\\\\\]))\\\\\\\\s*(\\\\\\\\??))\\\\\\\\s*((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*))?[\\\\\\\\(])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.property.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.generator.asterisk.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\(|\\\\\\\\<)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#method-declaration-name\\\"}]}]},\\\"object-member\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#object-literal-method-declaration\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\\[)\\\",\\\"end\\\":\\\"(?=:)|((?<=[\\\\\\\\]])(?=\\\\\\\\s*[\\\\\\\\(\\\\\\\\<]))\\\",\\\"name\\\":\\\"meta.object.member.js.jsx meta.object-literal.key.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#array-literal\\\"}]},{\\\"begin\\\":\\\"(?=[\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`])\\\",\\\"end\\\":\\\"(?=:)|((?<=[\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`])(?=((\\\\\\\\s*[\\\\\\\\(\\\\\\\\<,}])|(\\\\\\\\s+(as|satisifies)\\\\\\\\s+))))\\\",\\\"name\\\":\\\"meta.object.member.js.jsx meta.object-literal.key.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#string\\\"}]},{\\\"begin\\\":\\\"(?=(\\\\\\\\b(?<!\\\\\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:b|B)[01][01_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|((?<!\\\\\\\\$)(?:(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)(n)?\\\\\\\\B)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(n)?\\\\\\\\b(?!\\\\\\\\.)))(?!\\\\\\\\$)))\\\",\\\"end\\\":\\\"(?=:)|(?=\\\\\\\\s*([\\\\\\\\(\\\\\\\\<,}])|(\\\\\\\\s+as|satisifies\\\\\\\\s+))\\\",\\\"name\\\":\\\"meta.object.member.js.jsx meta.object-literal.key.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#numeric-literal\\\"}]},{\\\"begin\\\":\\\"(?<=[\\\\\\\\]\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`])(?=\\\\\\\\s*[\\\\\\\\(\\\\\\\\<])\\\",\\\"end\\\":\\\"(?=\\\\\\\\}|;|,)|(?<=\\\\\\\\})\\\",\\\"name\\\":\\\"meta.method.declaration.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-body\\\"}]},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.object-literal.key.js.jsx\\\"},\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.decimal.js.jsx\\\"}},\\\"match\\\":\\\"(?![_$[:alpha:]])([[:digit:]]+)\\\\\\\\s*(?=(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*:)\\\",\\\"name\\\":\\\"meta.object.member.js.jsx\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.object-literal.key.js.jsx\\\"},\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.js.jsx\\\"}},\\\"match\\\":\\\"(?:([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(?=(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*:(\\\\\\\\s*\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/)*\\\\\\\\s*(((async\\\\\\\\s+)?((function\\\\\\\\s*[(<*])|(function\\\\\\\\s+)|([_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*=>)))|((async\\\\\\\\s*)?(((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*))))))|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?[(]\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([)]\\\\\\\\s*:)|((\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*)?[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*:)))|([<]\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s+extends\\\\\\\\s*[^=>])|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?\\\\\\\\(\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([_$[:alpha:]]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*[_$[:alpha:]]))([^()\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))*)?\\\\\\\\)(\\\\\\\\s*:\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+)?\\\\\\\\s*=>))))))\\\",\\\"name\\\":\\\"meta.object.member.js.jsx\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.object-literal.key.js.jsx\\\"}},\\\"match\\\":\\\"(?:[_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(?=(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*:)\\\",\\\"name\\\":\\\"meta.object.member.js.jsx\\\"},{\\\"begin\\\":\\\"\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.spread.js.jsx\\\"}},\\\"end\\\":\\\"(?=,|\\\\\\\\})\\\",\\\"name\\\":\\\"meta.object.member.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.readwrite.js.jsx\\\"}},\\\"match\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(?=,|\\\\\\\\}|$|\\\\\\\\/\\\\\\\\/|\\\\\\\\/\\\\\\\\*)\\\",\\\"name\\\":\\\"meta.object.member.js.jsx\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.as.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(as)\\\\\\\\s+(const)(?=\\\\\\\\s*([,}]|$))\\\",\\\"name\\\":\\\"meta.object.member.js.jsx\\\"},{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(as)|(satisfies))\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.as.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.satisfies.js.jsx\\\"}},\\\"end\\\":\\\"(?=[;),}\\\\\\\\]:?\\\\\\\\-\\\\\\\\+\\\\\\\\>]|\\\\\\\\|\\\\\\\\||\\\\\\\\&\\\\\\\\&|\\\\\\\\!\\\\\\\\=\\\\\\\\=|$|^|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(as|satisifies)\\\\\\\\s+))\\\",\\\"name\\\":\\\"meta.object.member.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},{\\\"begin\\\":\\\"(?=[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*=)\\\",\\\"end\\\":\\\"(?=,|\\\\\\\\}|$|\\\\\\\\/\\\\\\\\/|\\\\\\\\/\\\\\\\\*)\\\",\\\"name\\\":\\\"meta.object.member.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},{\\\"begin\\\":\\\":\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.object-literal.key.js.jsx punctuation.separator.key-value.js.jsx\\\"}},\\\"end\\\":\\\"(?=,|\\\\\\\\})\\\",\\\"name\\\":\\\"meta.object.member.js.jsx\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=:)\\\\\\\\s*(async)?(?=\\\\\\\\s*(<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)\\\\\\\\(\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-inside-possibly-arrow-parens\\\"}]}]},{\\\"begin\\\":\\\"(?<=:)\\\\\\\\s*(async)?\\\\\\\\s*(\\\\\\\\()(?=\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-inside-possibly-arrow-parens\\\"}]},{\\\"begin\\\":\\\"(?<=:)\\\\\\\\s*(async)?\\\\\\\\s*(?=\\\\\\\\<\\\\\\\\s*$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\>)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-parameters\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\>)\\\\\\\\s*(\\\\\\\\()(?=\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-inside-possibly-arrow-parens\\\"}]},{\\\"include\\\":\\\"#possibly-arrow-return-type\\\"},{\\\"include\\\":\\\"#expression\\\"}]},{\\\"include\\\":\\\"#punctuation-comma\\\"},{\\\"include\\\":\\\"#decl-block\\\"}]},\\\"parameter-array-binding-pattern\\\":{\\\"begin\\\":\\\"(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.array.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.array.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parameter-binding-element\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"parameter-binding-element\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#numeric-literal\\\"},{\\\"include\\\":\\\"#regex\\\"},{\\\"include\\\":\\\"#parameter-object-binding-pattern\\\"},{\\\"include\\\":\\\"#parameter-array-binding-pattern\\\"},{\\\"include\\\":\\\"#destructuring-parameter-rest\\\"},{\\\"include\\\":\\\"#variable-initializer\\\"}]},\\\"parameter-name\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(override|public|protected|private|readonly)\\\\\\\\s+(?=(override|public|protected|private|readonly)\\\\\\\\s+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.js.jsx variable.language.this.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.function.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.optional.js.jsx\\\"}},\\\"match\\\":\\\"(?:(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(override|public|private|protected|readonly)\\\\\\\\s+)?(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?(?<!=|:)(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(this)|([_$[:alpha:]][_$[:alnum:]]*))(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\\\\\\s*(\\\\\\\\??)(?=\\\\\\\\s*(=\\\\\\\\s*(((async\\\\\\\\s+)?((function\\\\\\\\s*[(<*])|(function\\\\\\\\s+)|([_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*=>)))|((async\\\\\\\\s*)?(((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*))))))|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?[(]\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([)]\\\\\\\\s*:)|((\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*)?[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*:)))|([<]\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s+extends\\\\\\\\s*[^=>])|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?\\\\\\\\(\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([_$[:alpha:]]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*[_$[:alpha:]]))([^()\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))*)?\\\\\\\\)(\\\\\\\\s*:\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+)?\\\\\\\\s*=>)))))|(:\\\\\\\\s*((<)|([(]\\\\\\\\s*(([)])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.)|([_$[:alnum:]]+\\\\\\\\s*(([:,?=])|([)]\\\\\\\\s*=>)))))))|(:\\\\\\\\s*(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))Function(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))|(:\\\\\\\\s*((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))))))|(:\\\\\\\\s*(=>|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(<[^<>]*>)|[^<>(),=])+=\\\\\\\\s*(((async\\\\\\\\s+)?((function\\\\\\\\s*[(<*])|(function\\\\\\\\s+)|([_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*=>)))|((async\\\\\\\\s*)?(((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*))))))|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?[(]\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([)]\\\\\\\\s*:)|((\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*)?[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*:)))|([<]\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s+extends\\\\\\\\s*[^=>])|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?\\\\\\\\(\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([_$[:alpha:]]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*[_$[:alpha:]]))([^()\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))*)?\\\\\\\\)(\\\\\\\\s*:\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+)?\\\\\\\\s*=>))))))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.js.jsx variable.language.this.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.parameter.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.optional.js.jsx\\\"}},\\\"match\\\":\\\"(?:(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(override|public|private|protected|readonly)\\\\\\\\s+)?(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?(?<!=|:)(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(this)|([_$[:alpha:]][_$[:alnum:]]*))(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\\\\\\s*(\\\\\\\\??)\\\"}]},\\\"parameter-object-binding-element\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?=((\\\\\\\\b(?<!\\\\\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:b|B)[01][01_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|(\\\\\\\\b(?<!\\\\\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\\\\\b(?!\\\\\\\\$))|((?<!\\\\\\\\$)(?:(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)(n)?\\\\\\\\B)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(n)?\\\\\\\\b(?!\\\\\\\\.)))(?!\\\\\\\\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`)|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])+\\\\\\\\]))\\\\\\\\s*(:))\\\",\\\"end\\\":\\\"(?=,|\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#object-binding-element-propertyName\\\"},{\\\"include\\\":\\\"#parameter-binding-element\\\"},{\\\"include\\\":\\\"#paren-expression\\\"}]},{\\\"include\\\":\\\"#parameter-object-binding-pattern\\\"},{\\\"include\\\":\\\"#destructuring-parameter-rest\\\"},{\\\"include\\\":\\\"#variable-initializer\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"parameter-object-binding-pattern\\\":{\\\"begin\\\":\\\"(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.object.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.binding-pattern.object.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parameter-object-binding-element\\\"}]},\\\"parameter-type-annotation\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.type.annotation.js.jsx\\\"}},\\\"end\\\":\\\"(?=[,)])|(?==[^>])\\\",\\\"name\\\":\\\"meta.type.annotation.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]}]},\\\"paren-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"paren-expression-possibly-arrow\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=[(=,])\\\\\\\\s*(async)?(?=\\\\\\\\s*((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*))?\\\\\\\\(\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#paren-expression-possibly-arrow-with-typeparameters\\\"}]},{\\\"begin\\\":\\\"(?<=[(=,]|=>|^return|[^\\\\\\\\._$[:alnum:]]return)\\\\\\\\s*(async)?(?=\\\\\\\\s*((((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*))?\\\\\\\\()|(<)|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)))\\\\\\\\s*$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.async.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#paren-expression-possibly-arrow-with-typeparameters\\\"}]},{\\\"include\\\":\\\"#possibly-arrow-return-type\\\"}]},\\\"paren-expression-possibly-arrow-with-typeparameters\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-inside-possibly-arrow-parens\\\"}]}]},\\\"possibly-arrow-return-type\\\":{\\\"begin\\\":\\\"(?<=\\\\\\\\)|^)\\\\\\\\s*(:)(?=\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*=>)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.arrow.js.jsx meta.return.type.arrow.js.jsx keyword.operator.type.annotation.js.jsx\\\"}},\\\"contentName\\\":\\\"meta.arrow.js.jsx meta.return.type.arrow.js.jsx\\\",\\\"end\\\":\\\"(?==>|\\\\\\\\{|(^\\\\\\\\s*(export|function|class|interface|let|var|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|const|import|enum|namespace|module|type|abstract|declare)\\\\\\\\s+))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#arrow-return-type-body\\\"}]},\\\"property-accessor\\\":{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(accessor|get|set)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"storage.type.property.js.jsx\\\"},\\\"punctuation-accessor\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.accessor.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.optional.js.jsx\\\"}},\\\"match\\\":\\\"(?:(\\\\\\\\.)|(\\\\\\\\?\\\\\\\\.(?!\\\\\\\\s*[[:digit:]])))\\\"},\\\"punctuation-comma\\\":{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.comma.js.jsx\\\"},\\\"punctuation-semicolon\\\":{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.terminator.statement.js.jsx\\\"},\\\"qstring-double\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.js.jsx\\\"}},\\\"end\\\":\\\"(\\\\\\\")|((?:[^\\\\\\\\\\\\\\\\\\\\\\\\n])$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.js.jsx\\\"}},\\\"name\\\":\\\"string.quoted.double.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-character-escape\\\"}]},\\\"qstring-single\\\":{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.js.jsx\\\"}},\\\"end\\\":\\\"(\\\\\\\\')|((?:[^\\\\\\\\\\\\\\\\\\\\\\\\n])$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.js.jsx\\\"}},\\\"name\\\":\\\"string.quoted.single.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-character-escape\\\"}]},\\\"regex\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<!\\\\\\\\+\\\\\\\\+|--|})(?<=[=(:,\\\\\\\\[?+!]|^return|[^\\\\\\\\._$[:alnum:]]return|^case|[^\\\\\\\\._$[:alnum:]]case|=>|&&|\\\\\\\\|\\\\\\\\||\\\\\\\\*\\\\\\\\/)\\\\\\\\s*(\\\\\\\\/)(?![\\\\\\\\/*])(?=(?:[^\\\\\\\\/\\\\\\\\\\\\\\\\\\\\\\\\[\\\\\\\\()]|\\\\\\\\\\\\\\\\.|\\\\\\\\[([^\\\\\\\\]\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)+\\\\\\\\]|\\\\\\\\(([^\\\\\\\\)\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)+\\\\\\\\))+\\\\\\\\/([dgimsuvy]+|(?![\\\\\\\\/\\\\\\\\*])|(?=\\\\\\\\/\\\\\\\\*))(?!\\\\\\\\s*[a-zA-Z0-9_$]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.js.jsx\\\"}},\\\"end\\\":\\\"(/)([dgimsuvy]*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.js.jsx\\\"}},\\\"name\\\":\\\"string.regexp.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp\\\"}]},{\\\"begin\\\":\\\"((?<![_$[:alnum:])\\\\\\\\]]|\\\\\\\\+\\\\\\\\+|--|}|\\\\\\\\*\\\\\\\\/)|((?<=^return|[^\\\\\\\\._$[:alnum:]]return|^case|[^\\\\\\\\._$[:alnum:]]case))\\\\\\\\s*)\\\\\\\\/(?![\\\\\\\\/*])(?=(?:[^\\\\\\\\/\\\\\\\\\\\\\\\\\\\\\\\\[]|\\\\\\\\\\\\\\\\.|\\\\\\\\[([^\\\\\\\\]\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\])+\\\\\\\\/([dgimsuvy]+|(?![\\\\\\\\/\\\\\\\\*])|(?=\\\\\\\\/\\\\\\\\*))(?!\\\\\\\\s*[a-zA-Z0-9_$]))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.js.jsx\\\"}},\\\"end\\\":\\\"(/)([dgimsuvy]*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.js.jsx\\\"}},\\\"name\\\":\\\"string.regexp.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp\\\"}]}]},\\\"regex-character-class\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[wWsSdDtrnvf]|\\\\\\\\.\\\",\\\"name\\\":\\\"constant.other.character-class.regexp\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4})\\\",\\\"name\\\":\\\"constant.character.numeric.regexp\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\c[A-Z]\\\",\\\"name\\\":\\\"constant.character.control.regexp\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.backslash.regexp\\\"}]},\\\"regexp\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[bB]|\\\\\\\\^|\\\\\\\\$\\\",\\\"name\\\":\\\"keyword.control.anchor.regexp\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.back-reference.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"variable.other.regexp\\\"}},\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[1-9]\\\\\\\\d*|\\\\\\\\\\\\\\\\k<([a-zA-Z_$][\\\\\\\\w$]*)>\\\"},{\\\"match\\\":\\\"[?+*]|\\\\\\\\{(\\\\\\\\d+,\\\\\\\\d+|\\\\\\\\d+,|,\\\\\\\\d+|\\\\\\\\d+)\\\\\\\\}\\\\\\\\??\\\",\\\"name\\\":\\\"keyword.operator.quantifier.regexp\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.or.regexp\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()((\\\\\\\\?=)|(\\\\\\\\?!)|(\\\\\\\\?<=)|(\\\\\\\\?<!))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.group.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.assertion.regexp\\\"},\\\"3\\\":{\\\"name\\\":\\\"meta.assertion.look-ahead.regexp\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.assertion.negative-look-ahead.regexp\\\"},\\\"5\\\":{\\\"name\\\":\\\"meta.assertion.look-behind.regexp\\\"},\\\"6\\\":{\\\"name\\\":\\\"meta.assertion.negative-look-behind.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.group.regexp\\\"}},\\\"name\\\":\\\"meta.group.assertion.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\((?:(\\\\\\\\?:)|(?:\\\\\\\\?<([a-zA-Z_$][\\\\\\\\w$]*)>))?\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.group.no-capture.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.regexp\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.regexp\\\"}},\\\"name\\\":\\\"meta.group.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\[)(\\\\\\\\^)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.character-class.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.negation.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.character-class.regexp\\\"}},\\\"name\\\":\\\"constant.other.character-class.set.regexp\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.numeric.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.character.control.regexp\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.escape.backslash.regexp\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.character.numeric.regexp\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.character.control.regexp\\\"},\\\"6\\\":{\\\"name\\\":\\\"constant.character.escape.backslash.regexp\\\"}},\\\"match\\\":\\\"(?:.|(\\\\\\\\\\\\\\\\(?:[0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}))|(\\\\\\\\\\\\\\\\c[A-Z])|(\\\\\\\\\\\\\\\\.))\\\\\\\\-(?:[^\\\\\\\\]\\\\\\\\\\\\\\\\]|(\\\\\\\\\\\\\\\\(?:[0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}))|(\\\\\\\\\\\\\\\\c[A-Z])|(\\\\\\\\\\\\\\\\.))\\\",\\\"name\\\":\\\"constant.other.character-class.range.regexp\\\"},{\\\"include\\\":\\\"#regex-character-class\\\"}]},{\\\"include\\\":\\\"#regex-character-class\\\"}]},\\\"return-type\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=\\\\\\\\))\\\\\\\\s*(:)(?=\\\\\\\\s*\\\\\\\\S)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.type.annotation.js.jsx\\\"}},\\\"end\\\":\\\"(?<![:|&])(?=$|^|[{};,]|//)\\\",\\\"name\\\":\\\"meta.return.type.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#return-type-core\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\))\\\\\\\\s*(:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.type.annotation.js.jsx\\\"}},\\\"end\\\":\\\"(?<![:|&])((?=[{};,]|//|^\\\\\\\\s*$)|((?<=\\\\\\\\S)(?=\\\\\\\\s*$)))\\\",\\\"name\\\":\\\"meta.return.type.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#return-type-core\\\"}]}]},\\\"return-type-core\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?<=[:|&])(?=\\\\\\\\s*\\\\\\\\{)\\\",\\\"end\\\":\\\"(?<=\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-object\\\"}]},{\\\"include\\\":\\\"#type-predicate-operator\\\"},{\\\"include\\\":\\\"#type\\\"}]},\\\"shebang\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.js.jsx\\\"}},\\\"match\\\":\\\"\\\\\\\\A(#!).*(?=$)\\\",\\\"name\\\":\\\"comment.line.shebang.js.jsx\\\"},\\\"single-line-comment-consuming-line-ending\\\":{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?((//)(?:\\\\\\\\s*((@)internal)(?=\\\\\\\\s|$))?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.line.double-slash.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.comment.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.internaldeclaration.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.decorator.internaldeclaration.js.jsx\\\"}},\\\"contentName\\\":\\\"comment.line.double-slash.js.jsx\\\",\\\"end\\\":\\\"(?=^)\\\"},\\\"statements\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#declaration\\\"},{\\\"include\\\":\\\"#control-statement\\\"},{\\\"include\\\":\\\"#after-operator-block-as-object-literal\\\"},{\\\"include\\\":\\\"#decl-block\\\"},{\\\"include\\\":\\\"#label\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-semicolon\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#comment\\\"}]},\\\"string\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#qstring-single\\\"},{\\\"include\\\":\\\"#qstring-double\\\"},{\\\"include\\\":\\\"#template\\\"}]},\\\"string-character-escape\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}|u\\\\\\\\{[0-9A-Fa-f]+\\\\\\\\}|[0-2][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.|$)\\\",\\\"name\\\":\\\"constant.character.escape.js.jsx\\\"},\\\"super-literal\\\":{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))super\\\\\\\\b(?!\\\\\\\\$)\\\",\\\"name\\\":\\\"variable.language.super.js.jsx\\\"},\\\"support-function-call-identifiers\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#literal\\\"},{\\\"include\\\":\\\"#support-objects\\\"},{\\\"include\\\":\\\"#object-identifiers\\\"},{\\\"include\\\":\\\"#punctuation-accessor\\\"},{\\\"match\\\":\\\"(?:(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))import(?=\\\\\\\\s*[\\\\\\\\(]\\\\\\\\s*[\\\\\\\\\\\\\\\"\\\\\\\\'\\\\\\\\`]))\\\",\\\"name\\\":\\\"keyword.operator.expression.import.js.jsx\\\"}]},\\\"support-objects\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(arguments)\\\\\\\\b(?!\\\\\\\\$)\\\",\\\"name\\\":\\\"variable.language.arguments.js.jsx\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(Promise)\\\\\\\\b(?!\\\\\\\\$)\\\",\\\"name\\\":\\\"support.class.promise.js.jsx\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.import.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.accessor.optional.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"support.variable.property.importmeta.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(import)\\\\\\\\s*(?:(\\\\\\\\.)|(\\\\\\\\?\\\\\\\\.(?!\\\\\\\\s*[[:digit:]])))\\\\\\\\s*(meta)\\\\\\\\b(?!\\\\\\\\$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.new.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.accessor.optional.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"support.variable.property.target.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(new)\\\\\\\\s*(?:(\\\\\\\\.)|(\\\\\\\\?\\\\\\\\.(?!\\\\\\\\s*[[:digit:]])))\\\\\\\\s*(target)\\\\\\\\b(?!\\\\\\\\$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.accessor.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.optional.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.variable.property.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"support.constant.js.jsx\\\"}},\\\"match\\\":\\\"(?:(\\\\\\\\.)|(\\\\\\\\?\\\\\\\\.(?!\\\\\\\\s*[[:digit:]])))\\\\\\\\s*(?:(?:(constructor|length|prototype|__proto__)\\\\\\\\b(?!\\\\\\\\$|\\\\\\\\s*(<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?\\\\\\\\())|(?:(EPSILON|MAX_SAFE_INTEGER|MAX_VALUE|MIN_SAFE_INTEGER|MIN_VALUE|NEGATIVE_INFINITY|POSITIVE_INFINITY)\\\\\\\\b(?!\\\\\\\\$)))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.type.object.module.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type.object.module.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.accessor.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.accessor.optional.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"support.type.object.module.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(exports)|(module)(?:(?:(\\\\\\\\.)|(\\\\\\\\?\\\\\\\\.(?!\\\\\\\\s*[[:digit:]])))(exports|id|filename|loaded|parent|children))?)\\\\\\\\b(?!\\\\\\\\$)\\\"}]},\\\"switch-statement\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?=\\\\\\\\bswitch\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"name\\\":\\\"switch-statement.expr.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(switch)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.switch.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"name\\\":\\\"switch-expression.expr.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\})\\\",\\\"name\\\":\\\"switch-block.expr.js.jsx\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(case|default(?=:))(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.switch.js.jsx\\\"}},\\\"end\\\":\\\"(?=:)\\\",\\\"name\\\":\\\"case-clause.expr.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},{\\\"begin\\\":\\\"(:)\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"case-clause.expr.js.jsx punctuation.definition.section.case-statement.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.block.js.jsx punctuation.definition.block.js.jsx\\\"}},\\\"contentName\\\":\\\"meta.block.js.jsx\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.block.js.jsx punctuation.definition.block.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#statements\\\"}]},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"case-clause.expr.js.jsx punctuation.definition.section.case-statement.js.jsx\\\"}},\\\"match\\\":\\\"(:)\\\"},{\\\"include\\\":\\\"#statements\\\"}]}]},\\\"template\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#template-call\\\"},{\\\"begin\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)?(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.tagged-template.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.template.js.jsx punctuation.definition.string.template.begin.js.jsx\\\"}},\\\"contentName\\\":\\\"string.template.js.jsx\\\",\\\"end\\\":\\\"`\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"string.template.js.jsx punctuation.definition.string.template.end.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#template-substitution-element\\\"},{\\\"include\\\":\\\"#string-character-escape\\\"}]}]},\\\"template-call\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(([_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*\\\\\\\\??\\\\\\\\.\\\\\\\\s*)*|(\\\\\\\\??\\\\\\\\.\\\\\\\\s*)?)([_$[:alpha:]][_$[:alnum:]]*)(<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>|\\\\\\\\<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))(([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>|\\\\\\\\<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>)*(?<!=)\\\\\\\\>))*(?<!=)\\\\\\\\>)*(?<!=)>\\\\\\\\s*)?`)\\\",\\\"end\\\":\\\"(?=`)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(([_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*\\\\\\\\??\\\\\\\\.\\\\\\\\s*)*|(\\\\\\\\??\\\\\\\\.\\\\\\\\s*)?)([_$[:alpha:]][_$[:alnum:]]*))\\\",\\\"end\\\":\\\"(?=(<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>|\\\\\\\\<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))(([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>|\\\\\\\\<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>)*(?<!=)\\\\\\\\>))*(?<!=)\\\\\\\\>)*(?<!=)>\\\\\\\\s*)?`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#support-function-call-identifiers\\\"},{\\\"match\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\",\\\"name\\\":\\\"entity.name.function.tagged-template.js.jsx\\\"}]},{\\\"include\\\":\\\"#type-arguments\\\"}]},{\\\"begin\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)?\\\\\\\\s*(?=(<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>|\\\\\\\\<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))(([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>|\\\\\\\\<\\\\\\\\s*(((keyof|infer|typeof|readonly)\\\\\\\\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))(?=\\\\\\\\s*([\\\\\\\\<\\\\\\\\>\\\\\\\\,\\\\\\\\.\\\\\\\\[]|=>|&(?!&)|\\\\\\\\|(?!\\\\\\\\|)))))([^<>\\\\\\\\(]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(?<==)\\\\\\\\>)*(?<!=)\\\\\\\\>))*(?<!=)\\\\\\\\>)*(?<!=)>\\\\\\\\s*)`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.tagged-template.js.jsx\\\"}},\\\"end\\\":\\\"(?=`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-arguments\\\"}]}]},\\\"template-substitution-element\\\":{\\\"begin\\\":\\\"\\\\\\\\$\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.begin.js.jsx\\\"}},\\\"contentName\\\":\\\"meta.embedded.line.js.jsx\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.end.js.jsx\\\"}},\\\"name\\\":\\\"meta.template.expression.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"template-type\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#template-call\\\"},{\\\"begin\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)?(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.tagged-template.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.template.js.jsx punctuation.definition.string.template.begin.js.jsx\\\"}},\\\"contentName\\\":\\\"string.template.js.jsx\\\",\\\"end\\\":\\\"`\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"string.template.js.jsx punctuation.definition.string.template.end.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#template-type-substitution-element\\\"},{\\\"include\\\":\\\"#string-character-escape\\\"}]}]},\\\"template-type-substitution-element\\\":{\\\"begin\\\":\\\"\\\\\\\\$\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.begin.js.jsx\\\"}},\\\"contentName\\\":\\\"meta.embedded.line.js.jsx\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.end.js.jsx\\\"}},\\\"name\\\":\\\"meta.template.expression.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},\\\"ternary-expression\\\":{\\\"begin\\\":\\\"(?!\\\\\\\\?\\\\\\\\.\\\\\\\\s*[^[:digit:]])(\\\\\\\\?)(?!\\\\\\\\?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.ternary.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.ternary.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"this-literal\\\":{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))this\\\\\\\\b(?!\\\\\\\\$)\\\",\\\"name\\\":\\\"variable.language.this.js.jsx\\\"},\\\"type\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#type-string\\\"},{\\\"include\\\":\\\"#numeric-literal\\\"},{\\\"include\\\":\\\"#type-primitive\\\"},{\\\"include\\\":\\\"#type-builtin-literals\\\"},{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"include\\\":\\\"#type-tuple\\\"},{\\\"include\\\":\\\"#type-object\\\"},{\\\"include\\\":\\\"#type-operators\\\"},{\\\"include\\\":\\\"#type-conditional\\\"},{\\\"include\\\":\\\"#type-fn-type-parameters\\\"},{\\\"include\\\":\\\"#type-paren-or-function-parameters\\\"},{\\\"include\\\":\\\"#type-function-return-type\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(readonly)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\\\\\\s*\\\"},{\\\"include\\\":\\\"#type-name\\\"}]},\\\"type-alias-declaration\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b(type)\\\\\\\\b\\\\\\\\s+([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.type.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.alias.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\}|;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.type.declaration.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"begin\\\":\\\"(=)\\\\\\\\s*(intrinsic)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.assignment.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.intrinsic.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\}|;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},{\\\"begin\\\":\\\"(=)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.assignment.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\}|;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]}]},\\\"type-annotation\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(:)(?=\\\\\\\\s*\\\\\\\\S)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.type.annotation.js.jsx\\\"}},\\\"end\\\":\\\"(?<![:|&])(?!\\\\\\\\s*[|&]\\\\\\\\s+)((?=^|[,);\\\\\\\\}\\\\\\\\]]|//)|(?==[^>])|((?<=[\\\\\\\\}>\\\\\\\\]\\\\\\\\)]|[_$[:alpha:]])\\\\\\\\s*(?=\\\\\\\\{)))\\\",\\\"name\\\":\\\"meta.type.annotation.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},{\\\"begin\\\":\\\"(:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.type.annotation.js.jsx\\\"}},\\\"end\\\":\\\"(?<![:|&])((?=[,);\\\\\\\\}\\\\\\\\]]|\\\\\\\\/\\\\\\\\/)|(?==[^>])|(?=^\\\\\\\\s*$)|((?<=[\\\\\\\\}>\\\\\\\\]\\\\\\\\)]|[_$[:alpha:]])\\\\\\\\s*(?=\\\\\\\\{)))\\\",\\\"name\\\":\\\"meta.type.annotation.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]}]},\\\"type-arguments\\\":{\\\"begin\\\":\\\"\\\\\\\\<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.typeparameters.begin.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\>\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.typeparameters.end.js.jsx\\\"}},\\\"name\\\":\\\"meta.type.parameters.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-arguments-body\\\"}]},\\\"type-arguments-body\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.type.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(_)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\"},{\\\"include\\\":\\\"#type\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"type-builtin-literals\\\":{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(this|true|false|undefined|null|object)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"support.type.builtin.js.jsx\\\"},\\\"type-conditional\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(extends)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"}},\\\"end\\\":\\\"(?<=:)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\?\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.ternary.js.jsx\\\"}},\\\"end\\\":\\\":\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.ternary.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},{\\\"include\\\":\\\"#type\\\"}]}]},\\\"type-fn-type-parameters\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(abstract)\\\\\\\\s+)?(new)\\\\\\\\b(?=\\\\\\\\s*\\\\\\\\<)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.type.constructor.js.jsx storage.modifier.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.type.constructor.js.jsx keyword.control.new.js.jsx\\\"}},\\\"end\\\":\\\"(?<=>)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#type-parameters\\\"}]},{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(abstract)\\\\\\\\s+)?(new)\\\\\\\\b\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.new.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.type.constructor.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-parameters\\\"}]},{\\\"begin\\\":\\\"((?=[(]\\\\\\\\s*(([)])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.)|([_$[:alnum:]]+\\\\\\\\s*(([:,?=])|([)]\\\\\\\\s*=>))))))\\\",\\\"end\\\":\\\"(?<=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.type.function.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-parameters\\\"}]}]},\\\"type-function-return-type\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(=>)(?=\\\\\\\\s*\\\\\\\\S)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.arrow.js.jsx\\\"}},\\\"end\\\":\\\"(?<!=>)(?<![|&])(?=[,\\\\\\\\]\\\\\\\\)\\\\\\\\{\\\\\\\\}=;>:\\\\\\\\?]|//|$)\\\",\\\"name\\\":\\\"meta.type.function.return.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-function-return-type-core\\\"}]},{\\\"begin\\\":\\\"=>\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"storage.type.function.arrow.js.jsx\\\"}},\\\"end\\\":\\\"(?<!=>)(?<![|&])((?=[,\\\\\\\\]\\\\\\\\)\\\\\\\\{\\\\\\\\}=;:\\\\\\\\?>]|//|^\\\\\\\\s*$)|((?<=\\\\\\\\S)(?=\\\\\\\\s*$)))\\\",\\\"name\\\":\\\"meta.type.function.return.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-function-return-type-core\\\"}]}]},\\\"type-function-return-type-core\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?<==>)(?=\\\\\\\\s*\\\\\\\\{)\\\",\\\"end\\\":\\\"(?<=\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-object\\\"}]},{\\\"include\\\":\\\"#type-predicate-operator\\\"},{\\\"include\\\":\\\"#type\\\"}]},\\\"type-infer\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.expression.infer.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.expression.extends.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(infer)\\\\\\\\s+([_$[:alpha:]][_$[:alnum:]]*)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))(?:\\\\\\\\s+(extends)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))?\\\",\\\"name\\\":\\\"meta.type.infer.js.jsx\\\"}]},\\\"type-name\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(?:(\\\\\\\\.)|(\\\\\\\\?\\\\\\\\.(?!\\\\\\\\s*[[:digit:]])))\\\\\\\\s*(<)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.module.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.accessor.optional.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.type.parameters.js.jsx punctuation.definition.typeparameters.begin.js.jsx\\\"}},\\\"contentName\\\":\\\"meta.type.parameters.js.jsx\\\",\\\"end\\\":\\\"(>)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.type.parameters.js.jsx punctuation.definition.typeparameters.end.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#type-arguments-body\\\"}]},{\\\"begin\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(<)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.type.parameters.js.jsx punctuation.definition.typeparameters.begin.js.jsx\\\"}},\\\"contentName\\\":\\\"meta.type.parameters.js.jsx\\\",\\\"end\\\":\\\"(>)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.type.parameters.js.jsx punctuation.definition.typeparameters.end.js.jsx\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#type-arguments-body\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.module.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.accessor.optional.js.jsx\\\"}},\\\"match\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(?:(\\\\\\\\.)|(\\\\\\\\?\\\\\\\\.(?!\\\\\\\\s*[[:digit:]])))\\\"},{\\\"match\\\":\\\"[_$[:alpha:]][_$[:alnum:]]*\\\",\\\"name\\\":\\\"entity.name.type.js.jsx\\\"}]},\\\"type-object\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.js.jsx\\\"}},\\\"name\\\":\\\"meta.object.type.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#method-declaration\\\"},{\\\"include\\\":\\\"#indexer-declaration\\\"},{\\\"include\\\":\\\"#indexer-mapped-type-declaration\\\"},{\\\"include\\\":\\\"#field-declaration\\\"},{\\\"include\\\":\\\"#type-annotation\\\"},{\\\"begin\\\":\\\"\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.spread.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\}|;|,|$)|(?<=\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},{\\\"include\\\":\\\"#punctuation-comma\\\"},{\\\"include\\\":\\\"#punctuation-semicolon\\\"},{\\\"include\\\":\\\"#type\\\"}]},\\\"type-operators\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#typeof-operator\\\"},{\\\"include\\\":\\\"#type-infer\\\"},{\\\"begin\\\":\\\"([&|])(?=\\\\\\\\s*\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.type.js.jsx\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-object\\\"}]},{\\\"begin\\\":\\\"[&|]\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.type.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\S)\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))keyof(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.operator.expression.keyof.js.jsx\\\"},{\\\"match\\\":\\\"(\\\\\\\\?|\\\\\\\\:)\\\",\\\"name\\\":\\\"keyword.operator.ternary.js.jsx\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))import(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"keyword.operator.expression.import.js.jsx\\\"}]},\\\"type-parameters\\\":{\\\"begin\\\":\\\"(<)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.typeparameters.begin.js.jsx\\\"}},\\\"end\\\":\\\"(>)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.typeparameters.end.js.jsx\\\"}},\\\"name\\\":\\\"meta.type.parameters.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(extends|in|out|const)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},{\\\"include\\\":\\\"#type\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"},{\\\"match\\\":\\\"(=)(?!>)\\\",\\\"name\\\":\\\"keyword.operator.assignment.js.jsx\\\"}]},\\\"type-paren-or-function-parameters\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.round.js.jsx\\\"}},\\\"name\\\":\\\"meta.type.paren.cover.js.jsx\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.js.jsx variable.language.this.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.function.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.optional.js.jsx\\\"}},\\\"match\\\":\\\"(?:(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(public|private|protected|readonly)\\\\\\\\s+)?(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?(?<!=|:)(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(this)|([_$[:alpha:]][_$[:alnum:]]*))\\\\\\\\s*(\\\\\\\\??)(?=\\\\\\\\s*(:\\\\\\\\s*((<)|([(]\\\\\\\\s*(([)])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.)|([_$[:alnum:]]+\\\\\\\\s*(([:,?=])|([)]\\\\\\\\s*=>)))))))|(:\\\\\\\\s*(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))Function(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))|(:\\\\\\\\s*((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*))))))))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.js.jsx variable.language.this.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.parameter.js.jsx\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.optional.js.jsx\\\"}},\\\"match\\\":\\\"(?:(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(public|private|protected|readonly)\\\\\\\\s+)?(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s*)?(?<!=|:)(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(this)|([_$[:alpha:]][_$[:alnum:]]*))\\\\\\\\s*(\\\\\\\\??)(?=:)\\\"},{\\\"include\\\":\\\"#type-annotation\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.parameter.js.jsx\\\"},{\\\"include\\\":\\\"#type\\\"}]},\\\"type-predicate-operator\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.type.asserts.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.js.jsx variable.language.this.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.js.jsx\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.expression.is.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(asserts)\\\\\\\\s+)?(?!asserts)(?:(this)|([_$[:alpha:]][_$[:alnum:]]*))\\\\\\\\s(is)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.type.asserts.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.js.jsx variable.language.this.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(asserts)\\\\\\\\s+(?!is)(?:(this)|([_$[:alpha:]][_$[:alnum:]]*))(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))asserts(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.operator.type.asserts.js.jsx\\\"},{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))is(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"keyword.operator.expression.is.js.jsx\\\"}]},\\\"type-primitive\\\":{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(string|number|bigint|boolean|symbol|any|void|never|unknown)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"support.type.primitive.js.jsx\\\"},\\\"type-string\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#qstring-single\\\"},{\\\"include\\\":\\\"#qstring-double\\\"},{\\\"include\\\":\\\"#template-type\\\"}]},\\\"type-tuple\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.square.js.jsx\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.brace.square.js.jsx\\\"}},\\\"name\\\":\\\"meta.type.tuple.js.jsx\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\",\\\"name\\\":\\\"keyword.operator.rest.js.jsx\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.label.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.optional.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.label.js.jsx\\\"}},\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))([_$[:alpha:]][_$[:alnum:]]*)\\\\\\\\s*(\\\\\\\\?)?\\\\\\\\s*(:)\\\"},{\\\"include\\\":\\\"#type\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"typeof-operator\\\":{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))typeof(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.expression.typeof.js.jsx\\\"}},\\\"end\\\":\\\"(?=[,);}\\\\\\\\]=>:&|{\\\\\\\\?]|(extends\\\\\\\\s+)|$|;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-arguments\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"undefined-literal\\\":{\\\"match\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))undefined(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\",\\\"name\\\":\\\"constant.language.undefined.js.jsx\\\"},\\\"var-expr\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b(var|let)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))\\\",\\\"end\\\":\\\"(?!(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b(var|let)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))((?=^|;|}|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+)|;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b))|((?<!^let|[^\\\\\\\\._$[:alnum:]]let|^var|[^\\\\\\\\._$[:alnum:]]var)(?=\\\\\\\\s*$)))\\\",\\\"name\\\":\\\"meta.var.expr.js.jsx\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b(var|let)(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\S)\\\"},{\\\"include\\\":\\\"#destructuring-variable\\\"},{\\\"include\\\":\\\"#var-single-variable\\\"},{\\\"include\\\":\\\"#variable-initializer\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(,)\\\\\\\\s*(?=$|\\\\\\\\/\\\\\\\\/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.comma.js.jsx\\\"}},\\\"end\\\":\\\"(?<!,)(((?==|;|}|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+)|^\\\\\\\\s*$))|((?<=\\\\\\\\S)(?=\\\\\\\\s*$)))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#single-line-comment-consuming-line-ending\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#destructuring-variable\\\"},{\\\"include\\\":\\\"#var-single-variable\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},{\\\"begin\\\":\\\"(?=(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b(const(?!\\\\\\\\s+enum\\\\\\\\b))(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.js.jsx\\\"}},\\\"end\\\":\\\"(?!(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b(const(?!\\\\\\\\s+enum\\\\\\\\b))(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))((?=^|;|}|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+)|;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b))|((?<!^const|[^\\\\\\\\._$[:alnum:]]const)(?=\\\\\\\\s*$)))\\\",\\\"name\\\":\\\"meta.var.expr.js.jsx\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b(const(?!\\\\\\\\s+enum\\\\\\\\b))(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\S)\\\"},{\\\"include\\\":\\\"#destructuring-const\\\"},{\\\"include\\\":\\\"#var-single-const\\\"},{\\\"include\\\":\\\"#variable-initializer\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(,)\\\\\\\\s*(?=$|\\\\\\\\/\\\\\\\\/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.comma.js.jsx\\\"}},\\\"end\\\":\\\"(?<!,)(((?==|;|}|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+)|^\\\\\\\\s*$))|((?<=\\\\\\\\S)(?=\\\\\\\\s*$)))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#single-line-comment-consuming-line-ending\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#destructuring-const\\\"},{\\\"include\\\":\\\"#var-single-const\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},{\\\"begin\\\":\\\"(?=(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b((?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b))(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.js.jsx\\\"}},\\\"end\\\":\\\"(?!(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b((?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b))(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))((?=;|}|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+)|;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b))|((?<!^using|[^\\\\\\\\._$[:alnum:]]using|^await\\\\\\\\s+using|[^\\\\\\\\._$[:alnum:]]await\\\\\\\\s+using)(?=\\\\\\\\s*$)))\\\",\\\"name\\\":\\\"meta.var.expr.js.jsx\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(?:(\\\\\\\\bexport)\\\\\\\\s+)?(?:(\\\\\\\\bdeclare)\\\\\\\\s+)?\\\\\\\\b((?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b))(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.))\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.export.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.js.jsx\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.js.jsx\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\S)\\\"},{\\\"include\\\":\\\"#var-single-const\\\"},{\\\"include\\\":\\\"#variable-initializer\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(,)\\\\\\\\s*((?!\\\\\\\\S)|(?=\\\\\\\\/\\\\\\\\/))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.comma.js.jsx\\\"}},\\\"end\\\":\\\"(?<!,)(((?==|;|}|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+)|^\\\\\\\\s*$))|((?<=\\\\\\\\S)(?=\\\\\\\\s*$)))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#single-line-comment-consuming-line-ending\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#var-single-const\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},{\\\"include\\\":\\\"#punctuation-comma\\\"}]}]},\\\"var-single-const\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)(?=\\\\\\\\s*(=\\\\\\\\s*(((async\\\\\\\\s+)?((function\\\\\\\\s*[(<*])|(function\\\\\\\\s+)|([_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*=>)))|((async\\\\\\\\s*)?(((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*))))))|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?[(]\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([)]\\\\\\\\s*:)|((\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*)?[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*:)))|([<]\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s+extends\\\\\\\\s*[^=>])|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?\\\\\\\\(\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([_$[:alpha:]]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*[_$[:alpha:]]))([^()\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))*)?\\\\\\\\)(\\\\\\\\s*:\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+)?\\\\\\\\s*=>)))))|(:\\\\\\\\s*((<)|([(]\\\\\\\\s*(([)])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.)|([_$[:alnum:]]+\\\\\\\\s*(([:,?=])|([)]\\\\\\\\s*=>)))))))|(:\\\\\\\\s*(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))Function(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))|(:\\\\\\\\s*((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))))))|(:\\\\\\\\s*(=>|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(<[^<>]*>)|[^<>(),=])+=\\\\\\\\s*(((async\\\\\\\\s+)?((function\\\\\\\\s*[(<*])|(function\\\\\\\\s+)|([_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*=>)))|((async\\\\\\\\s*)?(((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*))))))|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?[(]\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([)]\\\\\\\\s*:)|((\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*)?[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*:)))|([<]\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s+extends\\\\\\\\s*[^=>])|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?\\\\\\\\(\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([_$[:alpha:]]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*[_$[:alpha:]]))([^()\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))*)?\\\\\\\\)(\\\\\\\\s*:\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+)?\\\\\\\\s*=>))))))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.definition.variable.js.jsx variable.other.constant.js.jsx entity.name.function.js.jsx\\\"}},\\\"end\\\":\\\"(?=$|^|[;,=}]|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+)|(;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b)))\\\",\\\"name\\\":\\\"meta.var-single-variable.expr.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#var-single-variable-type-annotation\\\"}]},{\\\"begin\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.definition.variable.js.jsx variable.other.constant.js.jsx\\\"}},\\\"end\\\":\\\"(?=$|^|[;,=}]|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+)|(;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b)))\\\",\\\"name\\\":\\\"meta.var-single-variable.expr.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#var-single-variable-type-annotation\\\"}]}]},\\\"var-single-variable\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)(\\\\\\\\!)?(?=\\\\\\\\s*(=\\\\\\\\s*(((async\\\\\\\\s+)?((function\\\\\\\\s*[(<*])|(function\\\\\\\\s+)|([_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*=>)))|((async\\\\\\\\s*)?(((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*))))))|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?[(]\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([)]\\\\\\\\s*:)|((\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*)?[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*:)))|([<]\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s+extends\\\\\\\\s*[^=>])|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?\\\\\\\\(\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([_$[:alpha:]]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*[_$[:alpha:]]))([^()\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))*)?\\\\\\\\)(\\\\\\\\s*:\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+)?\\\\\\\\s*=>)))))|(:\\\\\\\\s*((<)|([(]\\\\\\\\s*(([)])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.)|([_$[:alnum:]]+\\\\\\\\s*(([:,?=])|([)]\\\\\\\\s*=>)))))))|(:\\\\\\\\s*(?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))Function(?![_$[:alnum:]])(?:(?=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?!\\\\\\\\.)))|(:\\\\\\\\s*((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))))))|(:\\\\\\\\s*(=>|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(<[^<>]*>)|[^<>(),=])+=\\\\\\\\s*(((async\\\\\\\\s+)?((function\\\\\\\\s*[(<*])|(function\\\\\\\\s+)|([_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*=>)))|((async\\\\\\\\s*)?(((<\\\\\\\\s*$)|([\\\\\\\\(]\\\\\\\\s*((([\\\\\\\\{\\\\\\\\[]\\\\\\\\s*)?$)|((\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\{?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*)))|((\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])\\\\\\\\s*((:\\\\\\\\s*\\\\\\\\[?$)|((\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+\\\\\\\\s*)?=\\\\\\\\s*))))))|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?[(]\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([)]\\\\\\\\s*:)|((\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*)?[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s*:)))|([<]\\\\\\\\s*[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\s+extends\\\\\\\\s*[^=>])|((<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<]|\\\\\\\\<\\\\\\\\s*(((const\\\\\\\\s+)?[_$[:alpha:]])|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\]))([^=<>]|=[^<])*\\\\\\\\>)*\\\\\\\\>)*>\\\\\\\\s*)?\\\\\\\\(\\\\\\\\s*(\\\\\\\\/\\\\\\\\*([^\\\\\\\\*]|(\\\\\\\\*[^\\\\\\\\/]))*\\\\\\\\*\\\\\\\\/\\\\\\\\s*)*(([_$[:alpha:]]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|(\\\\\\\\{([^\\\\\\\\{\\\\\\\\}]|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]*\\\\\\\\})*\\\\\\\\}))*\\\\\\\\})|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|(\\\\\\\\[([^\\\\\\\\[\\\\\\\\]]|\\\\\\\\[[^\\\\\\\\[\\\\\\\\]]*\\\\\\\\])*\\\\\\\\]))*\\\\\\\\])|(\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\s*[_$[:alpha:]]))([^()\\\\\\\\'\\\\\\\\\\\\\\\"\\\\\\\\`]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|(\\\\\\\\(([^\\\\\\\\(\\\\\\\\)]|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]*\\\\\\\\))*\\\\\\\\)))*\\\\\\\\))|(\\\\\\\\'([^\\\\\\\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\')|(\\\\\\\\\\\\\\\"([^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\")|(\\\\\\\\`([^\\\\\\\\`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\`))*)?\\\\\\\\)(\\\\\\\\s*:\\\\\\\\s*([^<>\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]|\\\\\\\\<([^<>]|\\\\\\\\<([^<>]|\\\\\\\\<[^<>]+\\\\\\\\>)+\\\\\\\\>)+\\\\\\\\>|\\\\\\\\([^\\\\\\\\(\\\\\\\\)]+\\\\\\\\)|\\\\\\\\{[^\\\\\\\\{\\\\\\\\}]+\\\\\\\\})+)?\\\\\\\\s*=>))))))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.definition.variable.js.jsx entity.name.function.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.definiteassignment.js.jsx\\\"}},\\\"end\\\":\\\"(?=$|^|[;,=}]|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+)|(;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b)))\\\",\\\"name\\\":\\\"meta.var-single-variable.expr.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#var-single-variable-type-annotation\\\"}]},{\\\"begin\\\":\\\"([[:upper:]][_$[:digit:][:upper:]]*)(?![_$[:alnum:]])(\\\\\\\\!)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.definition.variable.js.jsx variable.other.constant.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.definiteassignment.js.jsx\\\"}},\\\"end\\\":\\\"(?=$|^|[;,=}]|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+)|(;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b)))\\\",\\\"name\\\":\\\"meta.var-single-variable.expr.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#var-single-variable-type-annotation\\\"}]},{\\\"begin\\\":\\\"([_$[:alpha:]][_$[:alnum:]]*)(\\\\\\\\!)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.definition.variable.js.jsx variable.other.readwrite.js.jsx\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.definiteassignment.js.jsx\\\"}},\\\"end\\\":\\\"(?=$|^|[;,=}]|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+)|(;|^\\\\\\\\s*$|(?:^\\\\\\\\s*(?:abstract|async|(?:\\\\\\\\bawait\\\\\\\\s+(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)\\\\\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\\\\\busing(?=\\\\\\\\s+(?!in\\\\\\\\b|of\\\\\\\\b(?!\\\\\\\\s*(?:of\\\\\\\\b|=)))[_$[:alpha:]])\\\\\\\\b)|var|while)\\\\\\\\b)))\\\",\\\"name\\\":\\\"meta.var-single-variable.expr.js.jsx\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#var-single-variable-type-annotation\\\"}]}]},\\\"var-single-variable-type-annotation\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-annotation\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#comment\\\"}]},\\\"variable-initializer\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<!=|!)(=)(?!=)(?=\\\\\\\\s*\\\\\\\\S)(?!\\\\\\\\s*.*=>\\\\\\\\s*$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.assignment.js.jsx\\\"}},\\\"end\\\":\\\"(?=$|^|[,);}\\\\\\\\]]|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},{\\\"begin\\\":\\\"(?<!=|!)(=)(?!=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.assignment.js.jsx\\\"}},\\\"end\\\":\\\"(?=[,);}\\\\\\\\]]|((?<![_$[:alnum:]])(?:(?<=\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(?<!\\\\\\\\.))(of|in)\\\\\\\\s+))|(?=^\\\\\\\\s*$)|(?<![\\\\\\\\|\\\\\\\\&\\\\\\\\+\\\\\\\\-\\\\\\\\*\\\\\\\\/])(?<=\\\\\\\\S)(?<!=)(?=\\\\\\\\s*$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]}]}},\\\"scopeName\\\":\\\"source.js.jsx\\\"}\"))\n\nexport default [\nlang\n]\n"], "mappings": ";AAAA,IAAM,OAAO,OAAO,OAAO,KAAK,MAAM,gvoMAA6h8M,CAAC;AAEpk8M,IAAO,cAAQ;AAAA,EACf;AACA;", "names": []}