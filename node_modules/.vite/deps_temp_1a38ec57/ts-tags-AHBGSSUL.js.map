{"version": 3, "sources": ["../../@shikijs/langs/dist/es-tag-css.mjs", "../../@shikijs/langs/dist/es-tag-glsl.mjs", "../../@shikijs/langs/dist/es-tag-html.mjs", "../../@shikijs/langs/dist/es-tag-sql.mjs", "../../@shikijs/langs/dist/es-tag-xml.mjs", "../../@shikijs/langs/dist/ts-tags.mjs"], "sourcesContent": ["import typescript from './typescript.mjs'\nimport css from './css.mjs'\nimport javascript from './javascript.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"fileTypes\\\":[\\\"js\\\",\\\"jsx\\\",\\\"ts\\\",\\\"tsx\\\",\\\"html\\\",\\\"vue\\\",\\\"svelte\\\",\\\"php\\\",\\\"res\\\"],\\\"injectTo\\\":[\\\"source.ts\\\",\\\"source.js\\\"],\\\"injectionSelector\\\":\\\"L:source.js -comment -string, L:source.js -comment -string, L:source.jsx -comment -string,  L:source.js.jsx -comment -string, L:source.ts -comment -string, L:source.tsx -comment -string, L:source.rescript -comment -string, L:source.vue -comment -string, L:source.svelte -comment -string, L:source.php -comment -string, L:source.rescript -comment -string\\\",\\\"injections\\\":{\\\"L:source\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"<\\\",\\\"name\\\":\\\"invalid.illegal.bad-angle-bracket.html\\\"}]}},\\\"name\\\":\\\"es-tag-css\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(\\\\\\\\s?\\\\\\\\/\\\\\\\\*\\\\\\\\s?(css|inline-css)\\\\\\\\s?\\\\\\\\*\\\\\\\\/\\\\\\\\s?)(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.css\\\"},{\\\"include\\\":\\\"inline.es6-htmlx#template\\\"}]},{\\\"begin\\\":\\\"(?i)(\\\\\\\\s*(css|inline-css))(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.css\\\"},{\\\"include\\\":\\\"inline.es6-htmlx#template\\\"},{\\\"include\\\":\\\"string.quoted.other.template.js\\\"}]},{\\\"begin\\\":\\\"(?i)(?<=\\\\\\\\s|\\\\\\\\,|\\\\\\\\=|\\\\\\\\:|\\\\\\\\(|\\\\\\\\$\\\\\\\\()\\\\\\\\s{0,}(((\\\\\\\\/\\\\\\\\*)|(\\\\\\\\/\\\\\\\\/))\\\\\\\\s?(css|inline-css)[ ]{0,1000}\\\\\\\\*?\\\\\\\\/?)[ ]{0,1000}$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line\\\"}},\\\"end\\\":\\\"(`).*\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\G)\\\",\\\"end\\\":\\\"(`)\\\"},{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.css\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\${)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.js\\\"}]}],\\\"scopeName\\\":\\\"inline.es6-css\\\",\\\"embeddedLangs\\\":[\\\"typescript\\\",\\\"css\\\",\\\"javascript\\\"]}\"))\n\nexport default [\n...typescript,\n...css,\n...javascript,\nlang\n]\n", "import typescript from './typescript.mjs'\nimport glsl from './glsl.mjs'\nimport javascript from './javascript.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"fileTypes\\\":[\\\"js\\\",\\\"jsx\\\",\\\"ts\\\",\\\"tsx\\\",\\\"html\\\",\\\"vue\\\",\\\"svelte\\\",\\\"php\\\",\\\"res\\\"],\\\"injectTo\\\":[\\\"source.ts\\\",\\\"source.js\\\"],\\\"injectionSelector\\\":\\\"L:source.js -comment -string, L:source.js -comment -string, L:source.jsx -comment -string,  L:source.js.jsx -comment -string, L:source.ts -comment -string, L:source.tsx -comment -string, L:source.rescript -comment -string\\\",\\\"injections\\\":{\\\"L:source\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"<\\\",\\\"name\\\":\\\"invalid.illegal.bad-angle-bracket.html\\\"}]}},\\\"name\\\":\\\"es-tag-glsl\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(\\\\\\\\s?\\\\\\\\/\\\\\\\\*\\\\\\\\s?(glsl|inline-glsl)\\\\\\\\s?\\\\\\\\*\\\\\\\\/\\\\\\\\s?)(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.glsl\\\"},{\\\"include\\\":\\\"inline.es6-htmlx#template\\\"}]},{\\\"begin\\\":\\\"(?i)(\\\\\\\\s*(glsl|inline-glsl))(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.glsl\\\"},{\\\"include\\\":\\\"inline.es6-htmlx#template\\\"},{\\\"include\\\":\\\"string.quoted.other.template.js\\\"}]},{\\\"begin\\\":\\\"(?i)(?<=\\\\\\\\s|\\\\\\\\,|\\\\\\\\=|\\\\\\\\:|\\\\\\\\(|\\\\\\\\$\\\\\\\\()\\\\\\\\s{0,}(((\\\\\\\\/\\\\\\\\*)|(\\\\\\\\/\\\\\\\\/))\\\\\\\\s?(glsl|inline-glsl)[ ]{0,1000}\\\\\\\\*?\\\\\\\\/?)[ ]{0,1000}$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line\\\"}},\\\"end\\\":\\\"(`).*\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\G)\\\",\\\"end\\\":\\\"(`)\\\"},{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.glsl\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\${)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.js\\\"}]}],\\\"scopeName\\\":\\\"inline.es6-glsl\\\",\\\"embeddedLangs\\\":[\\\"typescript\\\",\\\"glsl\\\",\\\"javascript\\\"]}\"))\n\nexport default [\n...typescript,\n...glsl,\n...javascript,\nlang\n]\n", "import typescript from './typescript.mjs'\nimport html from './html.mjs'\nimport javascript from './javascript.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"fileTypes\\\":[\\\"js\\\",\\\"jsx\\\",\\\"ts\\\",\\\"tsx\\\",\\\"html\\\",\\\"vue\\\",\\\"svelte\\\",\\\"php\\\",\\\"res\\\"],\\\"injectTo\\\":[\\\"source.ts\\\",\\\"source.js\\\"],\\\"injectionSelector\\\":\\\"L:source.js -comment -string, L:source.js -comment -string, L:source.jsx -comment -string,  L:source.js.jsx -comment -string, L:source.ts -comment -string, L:source.tsx -comment -string, L:source.rescript -comment -string\\\",\\\"injections\\\":{\\\"L:source\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"<\\\",\\\"name\\\":\\\"invalid.illegal.bad-angle-bracket.html\\\"}]}},\\\"name\\\":\\\"es-tag-html\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(\\\\\\\\s?\\\\\\\\/\\\\\\\\*\\\\\\\\s?(html|template|inline-html|inline-template)\\\\\\\\s?\\\\\\\\*\\\\\\\\/\\\\\\\\s?)(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"text.html.basic\\\"},{\\\"include\\\":\\\"inline.es6-htmlx#template\\\"}]},{\\\"begin\\\":\\\"(?i)(\\\\\\\\s*(html|template|inline-html|inline-template))(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"text.html.basic\\\"},{\\\"include\\\":\\\"inline.es6-htmlx#template\\\"},{\\\"include\\\":\\\"string.quoted.other.template.js\\\"}]},{\\\"begin\\\":\\\"(?i)(?<=\\\\\\\\s|\\\\\\\\,|\\\\\\\\=|\\\\\\\\:|\\\\\\\\(|\\\\\\\\$\\\\\\\\()\\\\\\\\s{0,}(((\\\\\\\\/\\\\\\\\*)|(\\\\\\\\/\\\\\\\\/))\\\\\\\\s?(html|template|inline-html|inline-template)[ ]{0,1000}\\\\\\\\*?\\\\\\\\/?)[ ]{0,1000}$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line\\\"}},\\\"end\\\":\\\"(`).*\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\G)\\\",\\\"end\\\":\\\"(`)\\\"},{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"text.html.basic\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\${)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.js\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\$\\\\\\\\(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"end\\\":\\\"(`\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.js\\\"}]}],\\\"scopeName\\\":\\\"inline.es6-html\\\",\\\"embeddedLangs\\\":[\\\"typescript\\\",\\\"html\\\",\\\"javascript\\\"]}\"))\n\nexport default [\n...typescript,\n...html,\n...javascript,\nlang\n]\n", "import typescript from './typescript.mjs'\nimport sql from './sql.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"fileTypes\\\":[\\\"js\\\",\\\"jsx\\\",\\\"ts\\\",\\\"tsx\\\",\\\"html\\\",\\\"vue\\\",\\\"svelte\\\",\\\"php\\\",\\\"res\\\"],\\\"injectTo\\\":[\\\"source.ts\\\",\\\"source.js\\\"],\\\"injectionSelector\\\":\\\"L:source.js -comment -string, L:source.jsx -comment -string,  L:source.js.jsx -comment -string, L:source.ts -comment -string, L:source.tsx -comment -string, L:source.rescript -comment -string\\\",\\\"injections\\\":{\\\"L:source\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"<\\\",\\\"name\\\":\\\"invalid.illegal.bad-angle-bracket.html\\\"}]}},\\\"name\\\":\\\"es-tag-sql\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(\\\\\\\\w+\\\\\\\\.sql)\\\\\\\\s*(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.ts#string-character-escape\\\"},{\\\"include\\\":\\\"source.sql\\\"},{\\\"include\\\":\\\"source.plpgsql.postgres\\\"},{\\\"match\\\":\\\".\\\"}]},{\\\"begin\\\":\\\"(?i)(\\\\\\\\s?\\\\\\\\/?\\\\\\\\*?\\\\\\\\s?(sql|inline-sql)\\\\\\\\s?\\\\\\\\*?\\\\\\\\/?\\\\\\\\s?)(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.ts#string-character-escape\\\"},{\\\"include\\\":\\\"source.sql\\\"},{\\\"include\\\":\\\"source.plpgsql.postgres\\\"},{\\\"match\\\":\\\".\\\"}]},{\\\"begin\\\":\\\"(?i)(?<=\\\\\\\\s|\\\\\\\\,|\\\\\\\\=|\\\\\\\\:|\\\\\\\\(|\\\\\\\\$\\\\\\\\()\\\\\\\\s{0,}(((\\\\\\\\/\\\\\\\\*)|(\\\\\\\\/\\\\\\\\/))\\\\\\\\s?(sql|inline-sql)[ ]{0,1000}\\\\\\\\*?\\\\\\\\/?)[ ]{0,1000}$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\G)\\\",\\\"end\\\":\\\"(`)\\\"},{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.ts#string-character-escape\\\"},{\\\"include\\\":\\\"source.sql\\\"},{\\\"include\\\":\\\"source.plpgsql.postgres\\\"},{\\\"match\\\":\\\".\\\"}]}],\\\"scopeName\\\":\\\"inline.es6-sql\\\",\\\"embeddedLangs\\\":[\\\"typescript\\\",\\\"sql\\\"]}\"))\n\nexport default [\n...typescript,\n...sql,\nlang\n]\n", "import xml from './xml.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"fileTypes\\\":[\\\"js\\\",\\\"jsx\\\",\\\"ts\\\",\\\"tsx\\\",\\\"html\\\",\\\"vue\\\",\\\"svelte\\\",\\\"php\\\",\\\"res\\\"],\\\"injectTo\\\":[\\\"source.ts\\\",\\\"source.js\\\"],\\\"injectionSelector\\\":\\\"L:source.js -comment -string, L:source.js -comment -string, L:source.jsx -comment -string,  L:source.js.jsx -comment -string, L:source.ts -comment -string, L:source.tsx -comment -string, L:source.rescript -comment -string\\\",\\\"injections\\\":{\\\"L:source\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"<\\\",\\\"name\\\":\\\"invalid.illegal.bad-angle-bracket.html\\\"}]}},\\\"name\\\":\\\"es-tag-xml\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(\\\\\\\\s?\\\\\\\\/\\\\\\\\*\\\\\\\\s?(xml|svg|inline-svg|inline-xml)\\\\\\\\s?\\\\\\\\*\\\\\\\\/\\\\\\\\s?)(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml\\\"}]},{\\\"begin\\\":\\\"(?i)(\\\\\\\\s*(xml|inline-xml))(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml\\\"}]},{\\\"begin\\\":\\\"(?i)(?<=\\\\\\\\s|\\\\\\\\,|\\\\\\\\=|\\\\\\\\:|\\\\\\\\(|\\\\\\\\$\\\\\\\\()\\\\\\\\s{0,}(((\\\\\\\\/\\\\\\\\*)|(\\\\\\\\/\\\\\\\\/))\\\\\\\\s?(xml|svg|inline-svg|inline-xml)[ ]{0,1000}\\\\\\\\*?\\\\\\\\/?)[ ]{0,1000}$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line\\\"}},\\\"end\\\":\\\"(`).*\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\G)\\\",\\\"end\\\":\\\"(`)\\\"},{\\\"include\\\":\\\"text.xml\\\"}]}],\\\"scopeName\\\":\\\"inline.es6-xml\\\",\\\"embeddedLangs\\\":[\\\"xml\\\"]}\"))\n\nexport default [\n...xml,\nlang\n]\n", "import typescript from './typescript.mjs'\nimport es_tag_css from './es-tag-css.mjs'\nimport es_tag_glsl from './es-tag-glsl.mjs'\nimport es_tag_html from './es-tag-html.mjs'\nimport es_tag_sql from './es-tag-sql.mjs'\nimport es_tag_xml from './es-tag-xml.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"TypeScript with Tags\\\",\\\"name\\\":\\\"ts-tags\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts\\\"}],\\\"scopeName\\\":\\\"source.ts.tags\\\",\\\"embeddedLangs\\\":[\\\"typescript\\\",\\\"es-tag-css\\\",\\\"es-tag-glsl\\\",\\\"es-tag-html\\\",\\\"es-tag-sql\\\",\\\"es-tag-xml\\\"],\\\"aliases\\\":[\\\"lit\\\"]}\"))\n\nexport default [\n...typescript,\n...es_tag_css,\n...es_tag_glsl,\n...es_tag_html,\n...es_tag_sql,\n...es_tag_xml,\nlang\n]\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,OAAO,OAAO,OAAO,KAAK,MAAM,wxDAA09D,CAAC;AAEjgE,IAAO,qBAAQ;AAAA,EACf,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACA;;;ACPA,IAAMA,QAAO,OAAO,OAAO,KAAK,MAAM,gqDAAk2D,CAAC;AAEz4D,IAAO,sBAAQ;AAAA,EACf,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACHA;AACA;;;ACPA,IAAMC,QAAO,OAAO,OAAO,KAAK,MAAM,69DAAisE,CAAC;AAExuE,IAAO,sBAAQ;AAAA,EACf,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACHA;AACA;;;ACRA,IAAMC,QAAO,OAAO,OAAO,KAAK,MAAM,qkDAA2vD,CAAC;AAElyD,IAAO,qBAAQ;AAAA,EACf,GAAG;AAAA,EACH,GAAG;AAAA,EACHA;AACA;;;ACPA,IAAMC,QAAO,OAAO,OAAO,KAAK,MAAM,spCAA0xC,CAAC;AAEj0C,IAAO,qBAAQ;AAAA,EACf,GAAG;AAAA,EACHA;AACA;;;ACAA,IAAMC,QAAO,OAAO,OAAO,KAAK,MAAM,+OAAmR,CAAC;AAE1T,IAAO,kBAAQ;AAAA,EACf,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACHA;AACA;", "names": ["lang", "lang", "lang", "lang", "lang"]}