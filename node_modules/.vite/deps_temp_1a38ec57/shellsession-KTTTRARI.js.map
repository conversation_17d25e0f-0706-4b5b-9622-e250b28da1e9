{"version": 3, "sources": ["../../@shikijs/langs/dist/shellsession.mjs"], "sourcesContent": ["import shellscript from './shellscript.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Shell Session\\\",\\\"fileTypes\\\":[\\\"sh-session\\\"],\\\"name\\\":\\\"shellsession\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.prompt-prefix.shell-session\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.prompt.shell-session\\\"},\\\"3\\\":{\\\"name\\\":\\\"source.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.shell\\\"}]}},\\\"match\\\":\\\"^(?:((?:\\\\\\\\(\\\\\\\\S+\\\\\\\\)\\\\\\\\s*)?(?:sh\\\\\\\\S*?|\\\\\\\\w+\\\\\\\\S+[@:]\\\\\\\\S+(?:\\\\\\\\s+\\\\\\\\S+)?|\\\\\\\\[\\\\\\\\S+?[@:][^\\\\\\\\n]+?\\\\\\\\].*?))\\\\\\\\s*)?([>$#%❯➜]|\\\\\\\\p{Greek})\\\\\\\\s+(.*)$\\\"},{\\\"match\\\":\\\"^.+$\\\",\\\"name\\\":\\\"meta.output.shell-session\\\"}],\\\"scopeName\\\":\\\"text.shell-session\\\",\\\"embeddedLangs\\\":[\\\"shellscript\\\"],\\\"aliases\\\":[\\\"console\\\"]}\"))\n\nexport default [\n...shellscript,\nlang\n]\n"], "mappings": ";;;;;;AAEA,IAAM,OAAO,OAAO,OAAO,KAAK,MAAM,6lBAA6pB,CAAC;AAEpsB,IAAO,uBAAQ;AAAA,EACf,GAAG;AAAA,EACH;AACA;", "names": []}