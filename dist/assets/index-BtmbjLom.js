var ed=Object.defineProperty;var td=(e,t,n)=>t in e?ed(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var yr=(e,t,n)=>td(e,typeof t!="symbol"?t+"":t,n);import{_ as nd,g as rd}from"./shiki-CnzOCXsv.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerPolicy&&(o.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?o.credentials="include":l.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(l){if(l.ep)return;l.ep=!0;const o=n(l);fetch(l.href,o)}})();function ku(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Eu={exports:{}},xl={},Cu={exports:{}},z={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sr=Symbol.for("react.element"),ld=Symbol.for("react.portal"),od=Symbol.for("react.fragment"),sd=Symbol.for("react.strict_mode"),id=Symbol.for("react.profiler"),ud=Symbol.for("react.provider"),ad=Symbol.for("react.context"),cd=Symbol.for("react.forward_ref"),dd=Symbol.for("react.suspense"),fd=Symbol.for("react.memo"),pd=Symbol.for("react.lazy"),si=Symbol.iterator;function md(e){return e===null||typeof e!="object"?null:(e=si&&e[si]||e["@@iterator"],typeof e=="function"?e:null)}var Nu={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},_u=Object.assign,ju={};function hn(e,t,n){this.props=e,this.context=t,this.refs=ju,this.updater=n||Nu}hn.prototype.isReactComponent={};hn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};hn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Pu(){}Pu.prototype=hn.prototype;function as(e,t,n){this.props=e,this.context=t,this.refs=ju,this.updater=n||Nu}var cs=as.prototype=new Pu;cs.constructor=as;_u(cs,hn.prototype);cs.isPureReactComponent=!0;var ii=Array.isArray,Tu=Object.prototype.hasOwnProperty,ds={current:null},Lu={key:!0,ref:!0,__self:!0,__source:!0};function Ru(e,t,n){var r,l={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)Tu.call(t,r)&&!Lu.hasOwnProperty(r)&&(l[r]=t[r]);var i=arguments.length-2;if(i===1)l.children=n;else if(1<i){for(var u=Array(i),a=0;a<i;a++)u[a]=arguments[a+2];l.children=u}if(e&&e.defaultProps)for(r in i=e.defaultProps,i)l[r]===void 0&&(l[r]=i[r]);return{$$typeof:sr,type:e,key:o,ref:s,props:l,_owner:ds.current}}function hd(e,t){return{$$typeof:sr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function fs(e){return typeof e=="object"&&e!==null&&e.$$typeof===sr}function yd(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ui=/\/+/g;function Hl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?yd(""+e.key):t.toString(36)}function Dr(e,t,n,r,l){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case sr:case ld:s=!0}}if(s)return s=e,l=l(s),e=r===""?"."+Hl(s,0):r,ii(l)?(n="",e!=null&&(n=e.replace(ui,"$&/")+"/"),Dr(l,t,n,"",function(a){return a})):l!=null&&(fs(l)&&(l=hd(l,n+(!l.key||s&&s.key===l.key?"":(""+l.key).replace(ui,"$&/")+"/")+e)),t.push(l)),1;if(s=0,r=r===""?".":r+":",ii(e))for(var i=0;i<e.length;i++){o=e[i];var u=r+Hl(o,i);s+=Dr(o,t,n,u,l)}else if(u=md(e),typeof u=="function")for(e=u.call(e),i=0;!(o=e.next()).done;)o=o.value,u=r+Hl(o,i++),s+=Dr(o,t,n,u,l);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function vr(e,t,n){if(e==null)return e;var r=[],l=0;return Dr(e,r,"","",function(o){return t.call(n,o,l++)}),r}function vd(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var de={current:null},Ar={transition:null},gd={ReactCurrentDispatcher:de,ReactCurrentBatchConfig:Ar,ReactCurrentOwner:ds};function zu(){throw Error("act(...) is not supported in production builds of React.")}z.Children={map:vr,forEach:function(e,t,n){vr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return vr(e,function(){t++}),t},toArray:function(e){return vr(e,function(t){return t})||[]},only:function(e){if(!fs(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};z.Component=hn;z.Fragment=od;z.Profiler=id;z.PureComponent=as;z.StrictMode=sd;z.Suspense=dd;z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=gd;z.act=zu;z.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=_u({},e.props),l=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=ds.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var i=e.type.defaultProps;for(u in t)Tu.call(t,u)&&!Lu.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&i!==void 0?i[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){i=Array(u);for(var a=0;a<u;a++)i[a]=arguments[a+2];r.children=i}return{$$typeof:sr,type:e.type,key:l,ref:o,props:r,_owner:s}};z.createContext=function(e){return e={$$typeof:ad,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:ud,_context:e},e.Consumer=e};z.createElement=Ru;z.createFactory=function(e){var t=Ru.bind(null,e);return t.type=e,t};z.createRef=function(){return{current:null}};z.forwardRef=function(e){return{$$typeof:cd,render:e}};z.isValidElement=fs;z.lazy=function(e){return{$$typeof:pd,_payload:{_status:-1,_result:e},_init:vd}};z.memo=function(e,t){return{$$typeof:fd,type:e,compare:t===void 0?null:t}};z.startTransition=function(e){var t=Ar.transition;Ar.transition={};try{e()}finally{Ar.transition=t}};z.unstable_act=zu;z.useCallback=function(e,t){return de.current.useCallback(e,t)};z.useContext=function(e){return de.current.useContext(e)};z.useDebugValue=function(){};z.useDeferredValue=function(e){return de.current.useDeferredValue(e)};z.useEffect=function(e,t){return de.current.useEffect(e,t)};z.useId=function(){return de.current.useId()};z.useImperativeHandle=function(e,t,n){return de.current.useImperativeHandle(e,t,n)};z.useInsertionEffect=function(e,t){return de.current.useInsertionEffect(e,t)};z.useLayoutEffect=function(e,t){return de.current.useLayoutEffect(e,t)};z.useMemo=function(e,t){return de.current.useMemo(e,t)};z.useReducer=function(e,t,n){return de.current.useReducer(e,t,n)};z.useRef=function(e){return de.current.useRef(e)};z.useState=function(e){return de.current.useState(e)};z.useSyncExternalStore=function(e,t,n){return de.current.useSyncExternalStore(e,t,n)};z.useTransition=function(){return de.current.useTransition()};z.version="18.3.1";Cu.exports=z;var T=Cu.exports;const Ou=ku(T);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xd=T,wd=Symbol.for("react.element"),Sd=Symbol.for("react.fragment"),kd=Object.prototype.hasOwnProperty,Ed=xd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Cd={key:!0,ref:!0,__self:!0,__source:!0};function Mu(e,t,n){var r,l={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)kd.call(t,r)&&!Cd.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:wd,type:e,key:o,ref:s,props:l,_owner:Ed.current}}xl.Fragment=Sd;xl.jsx=Mu;xl.jsxs=Mu;Eu.exports=xl;var f=Eu.exports,ho={},Iu={exports:{}},ke={},Du={exports:{}},Au={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,L){var R=N.length;N.push(L);e:for(;0<R;){var Q=R-1>>>1,Z=N[Q];if(0<l(Z,L))N[Q]=L,N[R]=Z,R=Q;else break e}}function n(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var L=N[0],R=N.pop();if(R!==L){N[0]=R;e:for(var Q=0,Z=N.length,mr=Z>>>1;Q<mr;){var Nt=2*(Q+1)-1,$l=N[Nt],_t=Nt+1,hr=N[_t];if(0>l($l,R))_t<Z&&0>l(hr,$l)?(N[Q]=hr,N[_t]=R,Q=_t):(N[Q]=$l,N[Nt]=R,Q=Nt);else if(_t<Z&&0>l(hr,R))N[Q]=hr,N[_t]=R,Q=_t;else break e}}return L}function l(N,L){var R=N.sortIndex-L.sortIndex;return R!==0?R:N.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,i=s.now();e.unstable_now=function(){return s.now()-i}}var u=[],a=[],m=1,y=null,h=3,g=!1,v=!1,x=!1,k=typeof setTimeout=="function"?setTimeout:null,d=typeof clearTimeout=="function"?clearTimeout:null,c=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function p(N){for(var L=n(a);L!==null;){if(L.callback===null)r(a);else if(L.startTime<=N)r(a),L.sortIndex=L.expirationTime,t(u,L);else break;L=n(a)}}function w(N){if(x=!1,p(N),!v)if(n(u)!==null)v=!0,Fl(E);else{var L=n(a);L!==null&&Ul(w,L.startTime-N)}}function E(N,L){v=!1,x&&(x=!1,d(P),P=-1),g=!0;var R=h;try{for(p(L),y=n(u);y!==null&&(!(y.expirationTime>L)||N&&!Re());){var Q=y.callback;if(typeof Q=="function"){y.callback=null,h=y.priorityLevel;var Z=Q(y.expirationTime<=L);L=e.unstable_now(),typeof Z=="function"?y.callback=Z:y===n(u)&&r(u),p(L)}else r(u);y=n(u)}if(y!==null)var mr=!0;else{var Nt=n(a);Nt!==null&&Ul(w,Nt.startTime-L),mr=!1}return mr}finally{y=null,h=R,g=!1}}var j=!1,_=null,P=-1,W=5,O=-1;function Re(){return!(e.unstable_now()-O<W)}function xn(){if(_!==null){var N=e.unstable_now();O=N;var L=!0;try{L=_(!0,N)}finally{L?wn():(j=!1,_=null)}}else j=!1}var wn;if(typeof c=="function")wn=function(){c(xn)};else if(typeof MessageChannel<"u"){var oi=new MessageChannel,bc=oi.port2;oi.port1.onmessage=xn,wn=function(){bc.postMessage(null)}}else wn=function(){k(xn,0)};function Fl(N){_=N,j||(j=!0,wn())}function Ul(N,L){P=k(function(){N(e.unstable_now())},L)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){v||g||(v=!0,Fl(E))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):W=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(N){switch(h){case 1:case 2:case 3:var L=3;break;default:L=h}var R=h;h=L;try{return N()}finally{h=R}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,L){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var R=h;h=N;try{return L()}finally{h=R}},e.unstable_scheduleCallback=function(N,L,R){var Q=e.unstable_now();switch(typeof R=="object"&&R!==null?(R=R.delay,R=typeof R=="number"&&0<R?Q+R:Q):R=Q,N){case 1:var Z=-1;break;case 2:Z=250;break;case 5:Z=**********;break;case 4:Z=1e4;break;default:Z=5e3}return Z=R+Z,N={id:m++,callback:L,priorityLevel:N,startTime:R,expirationTime:Z,sortIndex:-1},R>Q?(N.sortIndex=R,t(a,N),n(u)===null&&N===n(a)&&(x?(d(P),P=-1):x=!0,Ul(w,R-Q))):(N.sortIndex=Z,t(u,N),v||g||(v=!0,Fl(E))),N},e.unstable_shouldYield=Re,e.unstable_wrapCallback=function(N){var L=h;return function(){var R=h;h=L;try{return N.apply(this,arguments)}finally{h=R}}}})(Au);Du.exports=Au;var Nd=Du.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _d=T,Se=Nd;function S(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Fu=new Set,Bn={};function Ut(e,t){sn(e,t),sn(e+"Capture",t)}function sn(e,t){for(Bn[e]=t,e=0;e<t.length;e++)Fu.add(t[e])}var be=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),yo=Object.prototype.hasOwnProperty,jd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ai={},ci={};function Pd(e){return yo.call(ci,e)?!0:yo.call(ai,e)?!1:jd.test(e)?ci[e]=!0:(ai[e]=!0,!1)}function Td(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Ld(e,t,n,r){if(t===null||typeof t>"u"||Td(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function fe(e,t,n,r,l,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var re={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){re[e]=new fe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];re[t]=new fe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){re[e]=new fe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){re[e]=new fe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){re[e]=new fe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){re[e]=new fe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){re[e]=new fe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){re[e]=new fe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){re[e]=new fe(e,5,!1,e.toLowerCase(),null,!1,!1)});var ps=/[\-:]([a-z])/g;function ms(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ps,ms);re[t]=new fe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ps,ms);re[t]=new fe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ps,ms);re[t]=new fe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){re[e]=new fe(e,1,!1,e.toLowerCase(),null,!1,!1)});re.xlinkHref=new fe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){re[e]=new fe(e,1,!1,e.toLowerCase(),null,!0,!0)});function hs(e,t,n,r){var l=re.hasOwnProperty(t)?re[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Ld(t,n,l,r)&&(n=null),r||l===null?Pd(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var rt=_d.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,gr=Symbol.for("react.element"),Bt=Symbol.for("react.portal"),Vt=Symbol.for("react.fragment"),ys=Symbol.for("react.strict_mode"),vo=Symbol.for("react.profiler"),Uu=Symbol.for("react.provider"),$u=Symbol.for("react.context"),vs=Symbol.for("react.forward_ref"),go=Symbol.for("react.suspense"),xo=Symbol.for("react.suspense_list"),gs=Symbol.for("react.memo"),ot=Symbol.for("react.lazy"),Hu=Symbol.for("react.offscreen"),di=Symbol.iterator;function Sn(e){return e===null||typeof e!="object"?null:(e=di&&e[di]||e["@@iterator"],typeof e=="function"?e:null)}var V=Object.assign,Bl;function Tn(e){if(Bl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Bl=t&&t[1]||""}return`
`+Bl+e}var Vl=!1;function Kl(e,t){if(!e||Vl)return"";Vl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(a){var r=a}Reflect.construct(e,[],t)}else{try{t.call()}catch(a){r=a}e.call(t.prototype)}else{try{throw Error()}catch(a){r=a}e()}}catch(a){if(a&&r&&typeof a.stack=="string"){for(var l=a.stack.split(`
`),o=r.stack.split(`
`),s=l.length-1,i=o.length-1;1<=s&&0<=i&&l[s]!==o[i];)i--;for(;1<=s&&0<=i;s--,i--)if(l[s]!==o[i]){if(s!==1||i!==1)do if(s--,i--,0>i||l[s]!==o[i]){var u=`
`+l[s].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=s&&0<=i);break}}}finally{Vl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Tn(e):""}function Rd(e){switch(e.tag){case 5:return Tn(e.type);case 16:return Tn("Lazy");case 13:return Tn("Suspense");case 19:return Tn("SuspenseList");case 0:case 2:case 15:return e=Kl(e.type,!1),e;case 11:return e=Kl(e.type.render,!1),e;case 1:return e=Kl(e.type,!0),e;default:return""}}function wo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Vt:return"Fragment";case Bt:return"Portal";case vo:return"Profiler";case ys:return"StrictMode";case go:return"Suspense";case xo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case $u:return(e.displayName||"Context")+".Consumer";case Uu:return(e._context.displayName||"Context")+".Provider";case vs:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case gs:return t=e.displayName||null,t!==null?t:wo(e.type)||"Memo";case ot:t=e._payload,e=e._init;try{return wo(e(t))}catch{}}return null}function zd(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return wo(t);case 8:return t===ys?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function xt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Bu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Od(e){var t=Bu(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function xr(e){e._valueTracker||(e._valueTracker=Od(e))}function Vu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Bu(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Xr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function So(e,t){var n=t.checked;return V({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function fi(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=xt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Ku(e,t){t=t.checked,t!=null&&hs(e,"checked",t,!1)}function ko(e,t){Ku(e,t);var n=xt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Eo(e,t.type,n):t.hasOwnProperty("defaultValue")&&Eo(e,t.type,xt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function pi(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Eo(e,t,n){(t!=="number"||Xr(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ln=Array.isArray;function en(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+xt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Co(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(S(91));return V({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function mi(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(S(92));if(Ln(n)){if(1<n.length)throw Error(S(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:xt(n)}}function Wu(e,t){var n=xt(t.value),r=xt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function hi(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Qu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function No(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Qu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var wr,Gu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(wr=wr||document.createElement("div"),wr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=wr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Vn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var On={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Md=["Webkit","ms","Moz","O"];Object.keys(On).forEach(function(e){Md.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),On[t]=On[e]})});function Yu(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||On.hasOwnProperty(e)&&On[e]?(""+t).trim():t+"px"}function Xu(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=Yu(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var Id=V({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function _o(e,t){if(t){if(Id[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(S(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(S(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(S(61))}if(t.style!=null&&typeof t.style!="object")throw Error(S(62))}}function jo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Po=null;function xs(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var To=null,tn=null,nn=null;function yi(e){if(e=ar(e)){if(typeof To!="function")throw Error(S(280));var t=e.stateNode;t&&(t=Cl(t),To(e.stateNode,e.type,t))}}function Ju(e){tn?nn?nn.push(e):nn=[e]:tn=e}function Zu(){if(tn){var e=tn,t=nn;if(nn=tn=null,yi(e),t)for(e=0;e<t.length;e++)yi(t[e])}}function qu(e,t){return e(t)}function bu(){}var Wl=!1;function ea(e,t,n){if(Wl)return e(t,n);Wl=!0;try{return qu(e,t,n)}finally{Wl=!1,(tn!==null||nn!==null)&&(bu(),Zu())}}function Kn(e,t){var n=e.stateNode;if(n===null)return null;var r=Cl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(S(231,t,typeof n));return n}var Lo=!1;if(be)try{var kn={};Object.defineProperty(kn,"passive",{get:function(){Lo=!0}}),window.addEventListener("test",kn,kn),window.removeEventListener("test",kn,kn)}catch{Lo=!1}function Dd(e,t,n,r,l,o,s,i,u){var a=Array.prototype.slice.call(arguments,3);try{t.apply(n,a)}catch(m){this.onError(m)}}var Mn=!1,Jr=null,Zr=!1,Ro=null,Ad={onError:function(e){Mn=!0,Jr=e}};function Fd(e,t,n,r,l,o,s,i,u){Mn=!1,Jr=null,Dd.apply(Ad,arguments)}function Ud(e,t,n,r,l,o,s,i,u){if(Fd.apply(this,arguments),Mn){if(Mn){var a=Jr;Mn=!1,Jr=null}else throw Error(S(198));Zr||(Zr=!0,Ro=a)}}function $t(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function ta(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function vi(e){if($t(e)!==e)throw Error(S(188))}function $d(e){var t=e.alternate;if(!t){if(t=$t(e),t===null)throw Error(S(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var o=l.alternate;if(o===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===n)return vi(l),e;if(o===r)return vi(l),t;o=o.sibling}throw Error(S(188))}if(n.return!==r.return)n=l,r=o;else{for(var s=!1,i=l.child;i;){if(i===n){s=!0,n=l,r=o;break}if(i===r){s=!0,r=l,n=o;break}i=i.sibling}if(!s){for(i=o.child;i;){if(i===n){s=!0,n=o,r=l;break}if(i===r){s=!0,r=o,n=l;break}i=i.sibling}if(!s)throw Error(S(189))}}if(n.alternate!==r)throw Error(S(190))}if(n.tag!==3)throw Error(S(188));return n.stateNode.current===n?e:t}function na(e){return e=$d(e),e!==null?ra(e):null}function ra(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=ra(e);if(t!==null)return t;e=e.sibling}return null}var la=Se.unstable_scheduleCallback,gi=Se.unstable_cancelCallback,Hd=Se.unstable_shouldYield,Bd=Se.unstable_requestPaint,G=Se.unstable_now,Vd=Se.unstable_getCurrentPriorityLevel,ws=Se.unstable_ImmediatePriority,oa=Se.unstable_UserBlockingPriority,qr=Se.unstable_NormalPriority,Kd=Se.unstable_LowPriority,sa=Se.unstable_IdlePriority,wl=null,Be=null;function Wd(e){if(Be&&typeof Be.onCommitFiberRoot=="function")try{Be.onCommitFiberRoot(wl,e,void 0,(e.current.flags&128)===128)}catch{}}var De=Math.clz32?Math.clz32:Yd,Qd=Math.log,Gd=Math.LN2;function Yd(e){return e>>>=0,e===0?32:31-(Qd(e)/Gd|0)|0}var Sr=64,kr=4194304;function Rn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function br(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var i=s&~l;i!==0?r=Rn(i):(o&=s,o!==0&&(r=Rn(o)))}else s=n&~l,s!==0?r=Rn(s):o!==0&&(r=Rn(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-De(t),l=1<<n,r|=e[n],t&=~l;return r}function Xd(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Jd(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-De(o),i=1<<s,u=l[s];u===-1?(!(i&n)||i&r)&&(l[s]=Xd(i,t)):u<=t&&(e.expiredLanes|=i),o&=~i}}function zo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ia(){var e=Sr;return Sr<<=1,!(Sr&4194240)&&(Sr=64),e}function Ql(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ir(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-De(t),e[t]=n}function Zd(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-De(n),o=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~o}}function Ss(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-De(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var I=0;function ua(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var aa,ks,ca,da,fa,Oo=!1,Er=[],dt=null,ft=null,pt=null,Wn=new Map,Qn=new Map,it=[],qd="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function xi(e,t){switch(e){case"focusin":case"focusout":dt=null;break;case"dragenter":case"dragleave":ft=null;break;case"mouseover":case"mouseout":pt=null;break;case"pointerover":case"pointerout":Wn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Qn.delete(t.pointerId)}}function En(e,t,n,r,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[l]},t!==null&&(t=ar(t),t!==null&&ks(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function bd(e,t,n,r,l){switch(t){case"focusin":return dt=En(dt,e,t,n,r,l),!0;case"dragenter":return ft=En(ft,e,t,n,r,l),!0;case"mouseover":return pt=En(pt,e,t,n,r,l),!0;case"pointerover":var o=l.pointerId;return Wn.set(o,En(Wn.get(o)||null,e,t,n,r,l)),!0;case"gotpointercapture":return o=l.pointerId,Qn.set(o,En(Qn.get(o)||null,e,t,n,r,l)),!0}return!1}function pa(e){var t=Tt(e.target);if(t!==null){var n=$t(t);if(n!==null){if(t=n.tag,t===13){if(t=ta(n),t!==null){e.blockedOn=t,fa(e.priority,function(){ca(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Fr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Mo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Po=r,n.target.dispatchEvent(r),Po=null}else return t=ar(n),t!==null&&ks(t),e.blockedOn=n,!1;t.shift()}return!0}function wi(e,t,n){Fr(e)&&n.delete(t)}function ef(){Oo=!1,dt!==null&&Fr(dt)&&(dt=null),ft!==null&&Fr(ft)&&(ft=null),pt!==null&&Fr(pt)&&(pt=null),Wn.forEach(wi),Qn.forEach(wi)}function Cn(e,t){e.blockedOn===t&&(e.blockedOn=null,Oo||(Oo=!0,Se.unstable_scheduleCallback(Se.unstable_NormalPriority,ef)))}function Gn(e){function t(l){return Cn(l,e)}if(0<Er.length){Cn(Er[0],e);for(var n=1;n<Er.length;n++){var r=Er[n];r.blockedOn===e&&(r.blockedOn=null)}}for(dt!==null&&Cn(dt,e),ft!==null&&Cn(ft,e),pt!==null&&Cn(pt,e),Wn.forEach(t),Qn.forEach(t),n=0;n<it.length;n++)r=it[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<it.length&&(n=it[0],n.blockedOn===null);)pa(n),n.blockedOn===null&&it.shift()}var rn=rt.ReactCurrentBatchConfig,el=!0;function tf(e,t,n,r){var l=I,o=rn.transition;rn.transition=null;try{I=1,Es(e,t,n,r)}finally{I=l,rn.transition=o}}function nf(e,t,n,r){var l=I,o=rn.transition;rn.transition=null;try{I=4,Es(e,t,n,r)}finally{I=l,rn.transition=o}}function Es(e,t,n,r){if(el){var l=Mo(e,t,n,r);if(l===null)no(e,t,r,tl,n),xi(e,r);else if(bd(l,e,t,n,r))r.stopPropagation();else if(xi(e,r),t&4&&-1<qd.indexOf(e)){for(;l!==null;){var o=ar(l);if(o!==null&&aa(o),o=Mo(e,t,n,r),o===null&&no(e,t,r,tl,n),o===l)break;l=o}l!==null&&r.stopPropagation()}else no(e,t,r,null,n)}}var tl=null;function Mo(e,t,n,r){if(tl=null,e=xs(r),e=Tt(e),e!==null)if(t=$t(e),t===null)e=null;else if(n=t.tag,n===13){if(e=ta(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return tl=e,null}function ma(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Vd()){case ws:return 1;case oa:return 4;case qr:case Kd:return 16;case sa:return 536870912;default:return 16}default:return 16}}var at=null,Cs=null,Ur=null;function ha(){if(Ur)return Ur;var e,t=Cs,n=t.length,r,l="value"in at?at.value:at.textContent,o=l.length;for(e=0;e<n&&t[e]===l[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===l[o-r];r++);return Ur=l.slice(e,1<r?1-r:void 0)}function $r(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Cr(){return!0}function Si(){return!1}function Ee(e){function t(n,r,l,o,s){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var i in e)e.hasOwnProperty(i)&&(n=e[i],this[i]=n?n(o):o[i]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Cr:Si,this.isPropagationStopped=Si,this}return V(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Cr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Cr)},persist:function(){},isPersistent:Cr}),t}var yn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ns=Ee(yn),ur=V({},yn,{view:0,detail:0}),rf=Ee(ur),Gl,Yl,Nn,Sl=V({},ur,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_s,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Nn&&(Nn&&e.type==="mousemove"?(Gl=e.screenX-Nn.screenX,Yl=e.screenY-Nn.screenY):Yl=Gl=0,Nn=e),Gl)},movementY:function(e){return"movementY"in e?e.movementY:Yl}}),ki=Ee(Sl),lf=V({},Sl,{dataTransfer:0}),of=Ee(lf),sf=V({},ur,{relatedTarget:0}),Xl=Ee(sf),uf=V({},yn,{animationName:0,elapsedTime:0,pseudoElement:0}),af=Ee(uf),cf=V({},yn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),df=Ee(cf),ff=V({},yn,{data:0}),Ei=Ee(ff),pf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},mf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},hf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function yf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=hf[e])?!!t[e]:!1}function _s(){return yf}var vf=V({},ur,{key:function(e){if(e.key){var t=pf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=$r(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?mf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_s,charCode:function(e){return e.type==="keypress"?$r(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?$r(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),gf=Ee(vf),xf=V({},Sl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ci=Ee(xf),wf=V({},ur,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_s}),Sf=Ee(wf),kf=V({},yn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Ef=Ee(kf),Cf=V({},Sl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Nf=Ee(Cf),_f=[9,13,27,32],js=be&&"CompositionEvent"in window,In=null;be&&"documentMode"in document&&(In=document.documentMode);var jf=be&&"TextEvent"in window&&!In,ya=be&&(!js||In&&8<In&&11>=In),Ni=" ",_i=!1;function va(e,t){switch(e){case"keyup":return _f.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ga(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Kt=!1;function Pf(e,t){switch(e){case"compositionend":return ga(t);case"keypress":return t.which!==32?null:(_i=!0,Ni);case"textInput":return e=t.data,e===Ni&&_i?null:e;default:return null}}function Tf(e,t){if(Kt)return e==="compositionend"||!js&&va(e,t)?(e=ha(),Ur=Cs=at=null,Kt=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ya&&t.locale!=="ko"?null:t.data;default:return null}}var Lf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ji(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Lf[e.type]:t==="textarea"}function xa(e,t,n,r){Ju(r),t=nl(t,"onChange"),0<t.length&&(n=new Ns("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Dn=null,Yn=null;function Rf(e){La(e,0)}function kl(e){var t=Gt(e);if(Vu(t))return e}function zf(e,t){if(e==="change")return t}var wa=!1;if(be){var Jl;if(be){var Zl="oninput"in document;if(!Zl){var Pi=document.createElement("div");Pi.setAttribute("oninput","return;"),Zl=typeof Pi.oninput=="function"}Jl=Zl}else Jl=!1;wa=Jl&&(!document.documentMode||9<document.documentMode)}function Ti(){Dn&&(Dn.detachEvent("onpropertychange",Sa),Yn=Dn=null)}function Sa(e){if(e.propertyName==="value"&&kl(Yn)){var t=[];xa(t,Yn,e,xs(e)),ea(Rf,t)}}function Of(e,t,n){e==="focusin"?(Ti(),Dn=t,Yn=n,Dn.attachEvent("onpropertychange",Sa)):e==="focusout"&&Ti()}function Mf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return kl(Yn)}function If(e,t){if(e==="click")return kl(t)}function Df(e,t){if(e==="input"||e==="change")return kl(t)}function Af(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Fe=typeof Object.is=="function"?Object.is:Af;function Xn(e,t){if(Fe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!yo.call(t,l)||!Fe(e[l],t[l]))return!1}return!0}function Li(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ri(e,t){var n=Li(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Li(n)}}function ka(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ka(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ea(){for(var e=window,t=Xr();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Xr(e.document)}return t}function Ps(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Ff(e){var t=Ea(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ka(n.ownerDocument.documentElement,n)){if(r!==null&&Ps(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,o=Math.min(r.start,l);r=r.end===void 0?o:Math.min(r.end,l),!e.extend&&o>r&&(l=r,r=o,o=l),l=Ri(n,o);var s=Ri(n,r);l&&s&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Uf=be&&"documentMode"in document&&11>=document.documentMode,Wt=null,Io=null,An=null,Do=!1;function zi(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Do||Wt==null||Wt!==Xr(r)||(r=Wt,"selectionStart"in r&&Ps(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),An&&Xn(An,r)||(An=r,r=nl(Io,"onSelect"),0<r.length&&(t=new Ns("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Wt)))}function Nr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Qt={animationend:Nr("Animation","AnimationEnd"),animationiteration:Nr("Animation","AnimationIteration"),animationstart:Nr("Animation","AnimationStart"),transitionend:Nr("Transition","TransitionEnd")},ql={},Ca={};be&&(Ca=document.createElement("div").style,"AnimationEvent"in window||(delete Qt.animationend.animation,delete Qt.animationiteration.animation,delete Qt.animationstart.animation),"TransitionEvent"in window||delete Qt.transitionend.transition);function El(e){if(ql[e])return ql[e];if(!Qt[e])return e;var t=Qt[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ca)return ql[e]=t[n];return e}var Na=El("animationend"),_a=El("animationiteration"),ja=El("animationstart"),Pa=El("transitionend"),Ta=new Map,Oi="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function St(e,t){Ta.set(e,t),Ut(t,[e])}for(var bl=0;bl<Oi.length;bl++){var eo=Oi[bl],$f=eo.toLowerCase(),Hf=eo[0].toUpperCase()+eo.slice(1);St($f,"on"+Hf)}St(Na,"onAnimationEnd");St(_a,"onAnimationIteration");St(ja,"onAnimationStart");St("dblclick","onDoubleClick");St("focusin","onFocus");St("focusout","onBlur");St(Pa,"onTransitionEnd");sn("onMouseEnter",["mouseout","mouseover"]);sn("onMouseLeave",["mouseout","mouseover"]);sn("onPointerEnter",["pointerout","pointerover"]);sn("onPointerLeave",["pointerout","pointerover"]);Ut("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Ut("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Ut("onBeforeInput",["compositionend","keypress","textInput","paste"]);Ut("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Ut("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Ut("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Bf=new Set("cancel close invalid load scroll toggle".split(" ").concat(zn));function Mi(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Ud(r,t,void 0,e),e.currentTarget=null}function La(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var i=r[s],u=i.instance,a=i.currentTarget;if(i=i.listener,u!==o&&l.isPropagationStopped())break e;Mi(l,i,a),o=u}else for(s=0;s<r.length;s++){if(i=r[s],u=i.instance,a=i.currentTarget,i=i.listener,u!==o&&l.isPropagationStopped())break e;Mi(l,i,a),o=u}}}if(Zr)throw e=Ro,Zr=!1,Ro=null,e}function F(e,t){var n=t[Ho];n===void 0&&(n=t[Ho]=new Set);var r=e+"__bubble";n.has(r)||(Ra(t,e,2,!1),n.add(r))}function to(e,t,n){var r=0;t&&(r|=4),Ra(n,e,r,t)}var _r="_reactListening"+Math.random().toString(36).slice(2);function Jn(e){if(!e[_r]){e[_r]=!0,Fu.forEach(function(n){n!=="selectionchange"&&(Bf.has(n)||to(n,!1,e),to(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[_r]||(t[_r]=!0,to("selectionchange",!1,t))}}function Ra(e,t,n,r){switch(ma(t)){case 1:var l=tf;break;case 4:l=nf;break;default:l=Es}n=l.bind(null,t,n,e),l=void 0,!Lo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function no(e,t,n,r,l){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var i=r.stateNode.containerInfo;if(i===l||i.nodeType===8&&i.parentNode===l)break;if(s===4)for(s=r.return;s!==null;){var u=s.tag;if((u===3||u===4)&&(u=s.stateNode.containerInfo,u===l||u.nodeType===8&&u.parentNode===l))return;s=s.return}for(;i!==null;){if(s=Tt(i),s===null)return;if(u=s.tag,u===5||u===6){r=o=s;continue e}i=i.parentNode}}r=r.return}ea(function(){var a=o,m=xs(n),y=[];e:{var h=Ta.get(e);if(h!==void 0){var g=Ns,v=e;switch(e){case"keypress":if($r(n)===0)break e;case"keydown":case"keyup":g=gf;break;case"focusin":v="focus",g=Xl;break;case"focusout":v="blur",g=Xl;break;case"beforeblur":case"afterblur":g=Xl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=ki;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=of;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=Sf;break;case Na:case _a:case ja:g=af;break;case Pa:g=Ef;break;case"scroll":g=rf;break;case"wheel":g=Nf;break;case"copy":case"cut":case"paste":g=df;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Ci}var x=(t&4)!==0,k=!x&&e==="scroll",d=x?h!==null?h+"Capture":null:h;x=[];for(var c=a,p;c!==null;){p=c;var w=p.stateNode;if(p.tag===5&&w!==null&&(p=w,d!==null&&(w=Kn(c,d),w!=null&&x.push(Zn(c,w,p)))),k)break;c=c.return}0<x.length&&(h=new g(h,v,null,n,m),y.push({event:h,listeners:x}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",h&&n!==Po&&(v=n.relatedTarget||n.fromElement)&&(Tt(v)||v[et]))break e;if((g||h)&&(h=m.window===m?m:(h=m.ownerDocument)?h.defaultView||h.parentWindow:window,g?(v=n.relatedTarget||n.toElement,g=a,v=v?Tt(v):null,v!==null&&(k=$t(v),v!==k||v.tag!==5&&v.tag!==6)&&(v=null)):(g=null,v=a),g!==v)){if(x=ki,w="onMouseLeave",d="onMouseEnter",c="mouse",(e==="pointerout"||e==="pointerover")&&(x=Ci,w="onPointerLeave",d="onPointerEnter",c="pointer"),k=g==null?h:Gt(g),p=v==null?h:Gt(v),h=new x(w,c+"leave",g,n,m),h.target=k,h.relatedTarget=p,w=null,Tt(m)===a&&(x=new x(d,c+"enter",v,n,m),x.target=p,x.relatedTarget=k,w=x),k=w,g&&v)t:{for(x=g,d=v,c=0,p=x;p;p=Ht(p))c++;for(p=0,w=d;w;w=Ht(w))p++;for(;0<c-p;)x=Ht(x),c--;for(;0<p-c;)d=Ht(d),p--;for(;c--;){if(x===d||d!==null&&x===d.alternate)break t;x=Ht(x),d=Ht(d)}x=null}else x=null;g!==null&&Ii(y,h,g,x,!1),v!==null&&k!==null&&Ii(y,k,v,x,!0)}}e:{if(h=a?Gt(a):window,g=h.nodeName&&h.nodeName.toLowerCase(),g==="select"||g==="input"&&h.type==="file")var E=zf;else if(ji(h))if(wa)E=Df;else{E=Mf;var j=Of}else(g=h.nodeName)&&g.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(E=If);if(E&&(E=E(e,a))){xa(y,E,n,m);break e}j&&j(e,h,a),e==="focusout"&&(j=h._wrapperState)&&j.controlled&&h.type==="number"&&Eo(h,"number",h.value)}switch(j=a?Gt(a):window,e){case"focusin":(ji(j)||j.contentEditable==="true")&&(Wt=j,Io=a,An=null);break;case"focusout":An=Io=Wt=null;break;case"mousedown":Do=!0;break;case"contextmenu":case"mouseup":case"dragend":Do=!1,zi(y,n,m);break;case"selectionchange":if(Uf)break;case"keydown":case"keyup":zi(y,n,m)}var _;if(js)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else Kt?va(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(ya&&n.locale!=="ko"&&(Kt||P!=="onCompositionStart"?P==="onCompositionEnd"&&Kt&&(_=ha()):(at=m,Cs="value"in at?at.value:at.textContent,Kt=!0)),j=nl(a,P),0<j.length&&(P=new Ei(P,e,null,n,m),y.push({event:P,listeners:j}),_?P.data=_:(_=ga(n),_!==null&&(P.data=_)))),(_=jf?Pf(e,n):Tf(e,n))&&(a=nl(a,"onBeforeInput"),0<a.length&&(m=new Ei("onBeforeInput","beforeinput",null,n,m),y.push({event:m,listeners:a}),m.data=_))}La(y,t)})}function Zn(e,t,n){return{instance:e,listener:t,currentTarget:n}}function nl(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=Kn(e,n),o!=null&&r.unshift(Zn(e,o,l)),o=Kn(e,t),o!=null&&r.push(Zn(e,o,l))),e=e.return}return r}function Ht(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Ii(e,t,n,r,l){for(var o=t._reactName,s=[];n!==null&&n!==r;){var i=n,u=i.alternate,a=i.stateNode;if(u!==null&&u===r)break;i.tag===5&&a!==null&&(i=a,l?(u=Kn(n,o),u!=null&&s.unshift(Zn(n,u,i))):l||(u=Kn(n,o),u!=null&&s.push(Zn(n,u,i)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Vf=/\r\n?/g,Kf=/\u0000|\uFFFD/g;function Di(e){return(typeof e=="string"?e:""+e).replace(Vf,`
`).replace(Kf,"")}function jr(e,t,n){if(t=Di(t),Di(e)!==t&&n)throw Error(S(425))}function rl(){}var Ao=null,Fo=null;function Uo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var $o=typeof setTimeout=="function"?setTimeout:void 0,Wf=typeof clearTimeout=="function"?clearTimeout:void 0,Ai=typeof Promise=="function"?Promise:void 0,Qf=typeof queueMicrotask=="function"?queueMicrotask:typeof Ai<"u"?function(e){return Ai.resolve(null).then(e).catch(Gf)}:$o;function Gf(e){setTimeout(function(){throw e})}function ro(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),Gn(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);Gn(t)}function mt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Fi(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var vn=Math.random().toString(36).slice(2),He="__reactFiber$"+vn,qn="__reactProps$"+vn,et="__reactContainer$"+vn,Ho="__reactEvents$"+vn,Yf="__reactListeners$"+vn,Xf="__reactHandles$"+vn;function Tt(e){var t=e[He];if(t)return t;for(var n=e.parentNode;n;){if(t=n[et]||n[He]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Fi(e);e!==null;){if(n=e[He])return n;e=Fi(e)}return t}e=n,n=e.parentNode}return null}function ar(e){return e=e[He]||e[et],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Gt(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(S(33))}function Cl(e){return e[qn]||null}var Bo=[],Yt=-1;function kt(e){return{current:e}}function U(e){0>Yt||(e.current=Bo[Yt],Bo[Yt]=null,Yt--)}function A(e,t){Yt++,Bo[Yt]=e.current,e.current=t}var wt={},ie=kt(wt),he=kt(!1),Mt=wt;function un(e,t){var n=e.type.contextTypes;if(!n)return wt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in n)l[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function ye(e){return e=e.childContextTypes,e!=null}function ll(){U(he),U(ie)}function Ui(e,t,n){if(ie.current!==wt)throw Error(S(168));A(ie,t),A(he,n)}function za(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(S(108,zd(e)||"Unknown",l));return V({},n,r)}function ol(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||wt,Mt=ie.current,A(ie,e),A(he,he.current),!0}function $i(e,t,n){var r=e.stateNode;if(!r)throw Error(S(169));n?(e=za(e,t,Mt),r.__reactInternalMemoizedMergedChildContext=e,U(he),U(ie),A(ie,e)):U(he),A(he,n)}var Ge=null,Nl=!1,lo=!1;function Oa(e){Ge===null?Ge=[e]:Ge.push(e)}function Jf(e){Nl=!0,Oa(e)}function Et(){if(!lo&&Ge!==null){lo=!0;var e=0,t=I;try{var n=Ge;for(I=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ge=null,Nl=!1}catch(l){throw Ge!==null&&(Ge=Ge.slice(e+1)),la(ws,Et),l}finally{I=t,lo=!1}}return null}var Xt=[],Jt=0,sl=null,il=0,Ce=[],Ne=0,It=null,Xe=1,Je="";function jt(e,t){Xt[Jt++]=il,Xt[Jt++]=sl,sl=e,il=t}function Ma(e,t,n){Ce[Ne++]=Xe,Ce[Ne++]=Je,Ce[Ne++]=It,It=e;var r=Xe;e=Je;var l=32-De(r)-1;r&=~(1<<l),n+=1;var o=32-De(t)+l;if(30<o){var s=l-l%5;o=(r&(1<<s)-1).toString(32),r>>=s,l-=s,Xe=1<<32-De(t)+l|n<<l|r,Je=o+e}else Xe=1<<o|n<<l|r,Je=e}function Ts(e){e.return!==null&&(jt(e,1),Ma(e,1,0))}function Ls(e){for(;e===sl;)sl=Xt[--Jt],Xt[Jt]=null,il=Xt[--Jt],Xt[Jt]=null;for(;e===It;)It=Ce[--Ne],Ce[Ne]=null,Je=Ce[--Ne],Ce[Ne]=null,Xe=Ce[--Ne],Ce[Ne]=null}var we=null,xe=null,$=!1,Ie=null;function Ia(e,t){var n=je(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Hi(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,we=e,xe=mt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,we=e,xe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=It!==null?{id:Xe,overflow:Je}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=je(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,we=e,xe=null,!0):!1;default:return!1}}function Vo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ko(e){if($){var t=xe;if(t){var n=t;if(!Hi(e,t)){if(Vo(e))throw Error(S(418));t=mt(n.nextSibling);var r=we;t&&Hi(e,t)?Ia(r,n):(e.flags=e.flags&-4097|2,$=!1,we=e)}}else{if(Vo(e))throw Error(S(418));e.flags=e.flags&-4097|2,$=!1,we=e}}}function Bi(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;we=e}function Pr(e){if(e!==we)return!1;if(!$)return Bi(e),$=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Uo(e.type,e.memoizedProps)),t&&(t=xe)){if(Vo(e))throw Da(),Error(S(418));for(;t;)Ia(e,t),t=mt(t.nextSibling)}if(Bi(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(S(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){xe=mt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}xe=null}}else xe=we?mt(e.stateNode.nextSibling):null;return!0}function Da(){for(var e=xe;e;)e=mt(e.nextSibling)}function an(){xe=we=null,$=!1}function Rs(e){Ie===null?Ie=[e]:Ie.push(e)}var Zf=rt.ReactCurrentBatchConfig;function _n(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(S(309));var r=n.stateNode}if(!r)throw Error(S(147,e));var l=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var i=l.refs;s===null?delete i[o]:i[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(S(284));if(!n._owner)throw Error(S(290,e))}return e}function Tr(e,t){throw e=Object.prototype.toString.call(t),Error(S(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Vi(e){var t=e._init;return t(e._payload)}function Aa(e){function t(d,c){if(e){var p=d.deletions;p===null?(d.deletions=[c],d.flags|=16):p.push(c)}}function n(d,c){if(!e)return null;for(;c!==null;)t(d,c),c=c.sibling;return null}function r(d,c){for(d=new Map;c!==null;)c.key!==null?d.set(c.key,c):d.set(c.index,c),c=c.sibling;return d}function l(d,c){return d=gt(d,c),d.index=0,d.sibling=null,d}function o(d,c,p){return d.index=p,e?(p=d.alternate,p!==null?(p=p.index,p<c?(d.flags|=2,c):p):(d.flags|=2,c)):(d.flags|=1048576,c)}function s(d){return e&&d.alternate===null&&(d.flags|=2),d}function i(d,c,p,w){return c===null||c.tag!==6?(c=fo(p,d.mode,w),c.return=d,c):(c=l(c,p),c.return=d,c)}function u(d,c,p,w){var E=p.type;return E===Vt?m(d,c,p.props.children,w,p.key):c!==null&&(c.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===ot&&Vi(E)===c.type)?(w=l(c,p.props),w.ref=_n(d,c,p),w.return=d,w):(w=Gr(p.type,p.key,p.props,null,d.mode,w),w.ref=_n(d,c,p),w.return=d,w)}function a(d,c,p,w){return c===null||c.tag!==4||c.stateNode.containerInfo!==p.containerInfo||c.stateNode.implementation!==p.implementation?(c=po(p,d.mode,w),c.return=d,c):(c=l(c,p.children||[]),c.return=d,c)}function m(d,c,p,w,E){return c===null||c.tag!==7?(c=Ot(p,d.mode,w,E),c.return=d,c):(c=l(c,p),c.return=d,c)}function y(d,c,p){if(typeof c=="string"&&c!==""||typeof c=="number")return c=fo(""+c,d.mode,p),c.return=d,c;if(typeof c=="object"&&c!==null){switch(c.$$typeof){case gr:return p=Gr(c.type,c.key,c.props,null,d.mode,p),p.ref=_n(d,null,c),p.return=d,p;case Bt:return c=po(c,d.mode,p),c.return=d,c;case ot:var w=c._init;return y(d,w(c._payload),p)}if(Ln(c)||Sn(c))return c=Ot(c,d.mode,p,null),c.return=d,c;Tr(d,c)}return null}function h(d,c,p,w){var E=c!==null?c.key:null;if(typeof p=="string"&&p!==""||typeof p=="number")return E!==null?null:i(d,c,""+p,w);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case gr:return p.key===E?u(d,c,p,w):null;case Bt:return p.key===E?a(d,c,p,w):null;case ot:return E=p._init,h(d,c,E(p._payload),w)}if(Ln(p)||Sn(p))return E!==null?null:m(d,c,p,w,null);Tr(d,p)}return null}function g(d,c,p,w,E){if(typeof w=="string"&&w!==""||typeof w=="number")return d=d.get(p)||null,i(c,d,""+w,E);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case gr:return d=d.get(w.key===null?p:w.key)||null,u(c,d,w,E);case Bt:return d=d.get(w.key===null?p:w.key)||null,a(c,d,w,E);case ot:var j=w._init;return g(d,c,p,j(w._payload),E)}if(Ln(w)||Sn(w))return d=d.get(p)||null,m(c,d,w,E,null);Tr(c,w)}return null}function v(d,c,p,w){for(var E=null,j=null,_=c,P=c=0,W=null;_!==null&&P<p.length;P++){_.index>P?(W=_,_=null):W=_.sibling;var O=h(d,_,p[P],w);if(O===null){_===null&&(_=W);break}e&&_&&O.alternate===null&&t(d,_),c=o(O,c,P),j===null?E=O:j.sibling=O,j=O,_=W}if(P===p.length)return n(d,_),$&&jt(d,P),E;if(_===null){for(;P<p.length;P++)_=y(d,p[P],w),_!==null&&(c=o(_,c,P),j===null?E=_:j.sibling=_,j=_);return $&&jt(d,P),E}for(_=r(d,_);P<p.length;P++)W=g(_,d,P,p[P],w),W!==null&&(e&&W.alternate!==null&&_.delete(W.key===null?P:W.key),c=o(W,c,P),j===null?E=W:j.sibling=W,j=W);return e&&_.forEach(function(Re){return t(d,Re)}),$&&jt(d,P),E}function x(d,c,p,w){var E=Sn(p);if(typeof E!="function")throw Error(S(150));if(p=E.call(p),p==null)throw Error(S(151));for(var j=E=null,_=c,P=c=0,W=null,O=p.next();_!==null&&!O.done;P++,O=p.next()){_.index>P?(W=_,_=null):W=_.sibling;var Re=h(d,_,O.value,w);if(Re===null){_===null&&(_=W);break}e&&_&&Re.alternate===null&&t(d,_),c=o(Re,c,P),j===null?E=Re:j.sibling=Re,j=Re,_=W}if(O.done)return n(d,_),$&&jt(d,P),E;if(_===null){for(;!O.done;P++,O=p.next())O=y(d,O.value,w),O!==null&&(c=o(O,c,P),j===null?E=O:j.sibling=O,j=O);return $&&jt(d,P),E}for(_=r(d,_);!O.done;P++,O=p.next())O=g(_,d,P,O.value,w),O!==null&&(e&&O.alternate!==null&&_.delete(O.key===null?P:O.key),c=o(O,c,P),j===null?E=O:j.sibling=O,j=O);return e&&_.forEach(function(xn){return t(d,xn)}),$&&jt(d,P),E}function k(d,c,p,w){if(typeof p=="object"&&p!==null&&p.type===Vt&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case gr:e:{for(var E=p.key,j=c;j!==null;){if(j.key===E){if(E=p.type,E===Vt){if(j.tag===7){n(d,j.sibling),c=l(j,p.props.children),c.return=d,d=c;break e}}else if(j.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===ot&&Vi(E)===j.type){n(d,j.sibling),c=l(j,p.props),c.ref=_n(d,j,p),c.return=d,d=c;break e}n(d,j);break}else t(d,j);j=j.sibling}p.type===Vt?(c=Ot(p.props.children,d.mode,w,p.key),c.return=d,d=c):(w=Gr(p.type,p.key,p.props,null,d.mode,w),w.ref=_n(d,c,p),w.return=d,d=w)}return s(d);case Bt:e:{for(j=p.key;c!==null;){if(c.key===j)if(c.tag===4&&c.stateNode.containerInfo===p.containerInfo&&c.stateNode.implementation===p.implementation){n(d,c.sibling),c=l(c,p.children||[]),c.return=d,d=c;break e}else{n(d,c);break}else t(d,c);c=c.sibling}c=po(p,d.mode,w),c.return=d,d=c}return s(d);case ot:return j=p._init,k(d,c,j(p._payload),w)}if(Ln(p))return v(d,c,p,w);if(Sn(p))return x(d,c,p,w);Tr(d,p)}return typeof p=="string"&&p!==""||typeof p=="number"?(p=""+p,c!==null&&c.tag===6?(n(d,c.sibling),c=l(c,p),c.return=d,d=c):(n(d,c),c=fo(p,d.mode,w),c.return=d,d=c),s(d)):n(d,c)}return k}var cn=Aa(!0),Fa=Aa(!1),ul=kt(null),al=null,Zt=null,zs=null;function Os(){zs=Zt=al=null}function Ms(e){var t=ul.current;U(ul),e._currentValue=t}function Wo(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ln(e,t){al=e,zs=Zt=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(me=!0),e.firstContext=null)}function Te(e){var t=e._currentValue;if(zs!==e)if(e={context:e,memoizedValue:t,next:null},Zt===null){if(al===null)throw Error(S(308));Zt=e,al.dependencies={lanes:0,firstContext:e}}else Zt=Zt.next=e;return t}var Lt=null;function Is(e){Lt===null?Lt=[e]:Lt.push(e)}function Ua(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,Is(t)):(n.next=l.next,l.next=n),t.interleaved=n,tt(e,r)}function tt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var st=!1;function Ds(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function $a(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ze(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function ht(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,M&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,tt(e,n)}return l=r.interleaved,l===null?(t.next=t,Is(r)):(t.next=l.next,l.next=t),r.interleaved=t,tt(e,n)}function Hr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ss(e,n)}}function Ki(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?l=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?l=o=t:o=o.next=t}else l=o=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function cl(e,t,n,r){var l=e.updateQueue;st=!1;var o=l.firstBaseUpdate,s=l.lastBaseUpdate,i=l.shared.pending;if(i!==null){l.shared.pending=null;var u=i,a=u.next;u.next=null,s===null?o=a:s.next=a,s=u;var m=e.alternate;m!==null&&(m=m.updateQueue,i=m.lastBaseUpdate,i!==s&&(i===null?m.firstBaseUpdate=a:i.next=a,m.lastBaseUpdate=u))}if(o!==null){var y=l.baseState;s=0,m=a=u=null,i=o;do{var h=i.lane,g=i.eventTime;if((r&h)===h){m!==null&&(m=m.next={eventTime:g,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var v=e,x=i;switch(h=t,g=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){y=v.call(g,y,h);break e}y=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,h=typeof v=="function"?v.call(g,y,h):v,h==null)break e;y=V({},y,h);break e;case 2:st=!0}}i.callback!==null&&i.lane!==0&&(e.flags|=64,h=l.effects,h===null?l.effects=[i]:h.push(i))}else g={eventTime:g,lane:h,tag:i.tag,payload:i.payload,callback:i.callback,next:null},m===null?(a=m=g,u=y):m=m.next=g,s|=h;if(i=i.next,i===null){if(i=l.shared.pending,i===null)break;h=i,i=h.next,h.next=null,l.lastBaseUpdate=h,l.shared.pending=null}}while(!0);if(m===null&&(u=y),l.baseState=u,l.firstBaseUpdate=a,l.lastBaseUpdate=m,t=l.shared.interleaved,t!==null){l=t;do s|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);At|=s,e.lanes=s,e.memoizedState=y}}function Wi(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(S(191,l));l.call(r)}}}var cr={},Ve=kt(cr),bn=kt(cr),er=kt(cr);function Rt(e){if(e===cr)throw Error(S(174));return e}function As(e,t){switch(A(er,t),A(bn,e),A(Ve,cr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:No(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=No(t,e)}U(Ve),A(Ve,t)}function dn(){U(Ve),U(bn),U(er)}function Ha(e){Rt(er.current);var t=Rt(Ve.current),n=No(t,e.type);t!==n&&(A(bn,e),A(Ve,n))}function Fs(e){bn.current===e&&(U(Ve),U(bn))}var H=kt(0);function dl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var oo=[];function Us(){for(var e=0;e<oo.length;e++)oo[e]._workInProgressVersionPrimary=null;oo.length=0}var Br=rt.ReactCurrentDispatcher,so=rt.ReactCurrentBatchConfig,Dt=0,B=null,X=null,q=null,fl=!1,Fn=!1,tr=0,qf=0;function le(){throw Error(S(321))}function $s(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Fe(e[n],t[n]))return!1;return!0}function Hs(e,t,n,r,l,o){if(Dt=o,B=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Br.current=e===null||e.memoizedState===null?np:rp,e=n(r,l),Fn){o=0;do{if(Fn=!1,tr=0,25<=o)throw Error(S(301));o+=1,q=X=null,t.updateQueue=null,Br.current=lp,e=n(r,l)}while(Fn)}if(Br.current=pl,t=X!==null&&X.next!==null,Dt=0,q=X=B=null,fl=!1,t)throw Error(S(300));return e}function Bs(){var e=tr!==0;return tr=0,e}function $e(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return q===null?B.memoizedState=q=e:q=q.next=e,q}function Le(){if(X===null){var e=B.alternate;e=e!==null?e.memoizedState:null}else e=X.next;var t=q===null?B.memoizedState:q.next;if(t!==null)q=t,X=e;else{if(e===null)throw Error(S(310));X=e,e={memoizedState:X.memoizedState,baseState:X.baseState,baseQueue:X.baseQueue,queue:X.queue,next:null},q===null?B.memoizedState=q=e:q=q.next=e}return q}function nr(e,t){return typeof t=="function"?t(e):t}function io(e){var t=Le(),n=t.queue;if(n===null)throw Error(S(311));n.lastRenderedReducer=e;var r=X,l=r.baseQueue,o=n.pending;if(o!==null){if(l!==null){var s=l.next;l.next=o.next,o.next=s}r.baseQueue=l=o,n.pending=null}if(l!==null){o=l.next,r=r.baseState;var i=s=null,u=null,a=o;do{var m=a.lane;if((Dt&m)===m)u!==null&&(u=u.next={lane:0,action:a.action,hasEagerState:a.hasEagerState,eagerState:a.eagerState,next:null}),r=a.hasEagerState?a.eagerState:e(r,a.action);else{var y={lane:m,action:a.action,hasEagerState:a.hasEagerState,eagerState:a.eagerState,next:null};u===null?(i=u=y,s=r):u=u.next=y,B.lanes|=m,At|=m}a=a.next}while(a!==null&&a!==o);u===null?s=r:u.next=i,Fe(r,t.memoizedState)||(me=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do o=l.lane,B.lanes|=o,At|=o,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function uo(e){var t=Le(),n=t.queue;if(n===null)throw Error(S(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,o=t.memoizedState;if(l!==null){n.pending=null;var s=l=l.next;do o=e(o,s.action),s=s.next;while(s!==l);Fe(o,t.memoizedState)||(me=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Ba(){}function Va(e,t){var n=B,r=Le(),l=t(),o=!Fe(r.memoizedState,l);if(o&&(r.memoizedState=l,me=!0),r=r.queue,Vs(Qa.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||q!==null&&q.memoizedState.tag&1){if(n.flags|=2048,rr(9,Wa.bind(null,n,r,l,t),void 0,null),b===null)throw Error(S(349));Dt&30||Ka(n,t,l)}return l}function Ka(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=B.updateQueue,t===null?(t={lastEffect:null,stores:null},B.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Wa(e,t,n,r){t.value=n,t.getSnapshot=r,Ga(t)&&Ya(e)}function Qa(e,t,n){return n(function(){Ga(t)&&Ya(e)})}function Ga(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Fe(e,n)}catch{return!0}}function Ya(e){var t=tt(e,1);t!==null&&Ae(t,e,1,-1)}function Qi(e){var t=$e();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:nr,lastRenderedState:e},t.queue=e,e=e.dispatch=tp.bind(null,B,e),[t.memoizedState,e]}function rr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=B.updateQueue,t===null?(t={lastEffect:null,stores:null},B.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Xa(){return Le().memoizedState}function Vr(e,t,n,r){var l=$e();B.flags|=e,l.memoizedState=rr(1|t,n,void 0,r===void 0?null:r)}function _l(e,t,n,r){var l=Le();r=r===void 0?null:r;var o=void 0;if(X!==null){var s=X.memoizedState;if(o=s.destroy,r!==null&&$s(r,s.deps)){l.memoizedState=rr(t,n,o,r);return}}B.flags|=e,l.memoizedState=rr(1|t,n,o,r)}function Gi(e,t){return Vr(8390656,8,e,t)}function Vs(e,t){return _l(2048,8,e,t)}function Ja(e,t){return _l(4,2,e,t)}function Za(e,t){return _l(4,4,e,t)}function qa(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function ba(e,t,n){return n=n!=null?n.concat([e]):null,_l(4,4,qa.bind(null,t,e),n)}function Ks(){}function ec(e,t){var n=Le();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&$s(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function tc(e,t){var n=Le();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&$s(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function nc(e,t,n){return Dt&21?(Fe(n,t)||(n=ia(),B.lanes|=n,At|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,me=!0),e.memoizedState=n)}function bf(e,t){var n=I;I=n!==0&&4>n?n:4,e(!0);var r=so.transition;so.transition={};try{e(!1),t()}finally{I=n,so.transition=r}}function rc(){return Le().memoizedState}function ep(e,t,n){var r=vt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},lc(e))oc(t,n);else if(n=Ua(e,t,n,r),n!==null){var l=ce();Ae(n,e,r,l),sc(n,t,r)}}function tp(e,t,n){var r=vt(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(lc(e))oc(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,i=o(s,n);if(l.hasEagerState=!0,l.eagerState=i,Fe(i,s)){var u=t.interleaved;u===null?(l.next=l,Is(t)):(l.next=u.next,u.next=l),t.interleaved=l;return}}catch{}finally{}n=Ua(e,t,l,r),n!==null&&(l=ce(),Ae(n,e,r,l),sc(n,t,r))}}function lc(e){var t=e.alternate;return e===B||t!==null&&t===B}function oc(e,t){Fn=fl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function sc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ss(e,n)}}var pl={readContext:Te,useCallback:le,useContext:le,useEffect:le,useImperativeHandle:le,useInsertionEffect:le,useLayoutEffect:le,useMemo:le,useReducer:le,useRef:le,useState:le,useDebugValue:le,useDeferredValue:le,useTransition:le,useMutableSource:le,useSyncExternalStore:le,useId:le,unstable_isNewReconciler:!1},np={readContext:Te,useCallback:function(e,t){return $e().memoizedState=[e,t===void 0?null:t],e},useContext:Te,useEffect:Gi,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Vr(4194308,4,qa.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Vr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Vr(4,2,e,t)},useMemo:function(e,t){var n=$e();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=$e();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ep.bind(null,B,e),[r.memoizedState,e]},useRef:function(e){var t=$e();return e={current:e},t.memoizedState=e},useState:Qi,useDebugValue:Ks,useDeferredValue:function(e){return $e().memoizedState=e},useTransition:function(){var e=Qi(!1),t=e[0];return e=bf.bind(null,e[1]),$e().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=B,l=$e();if($){if(n===void 0)throw Error(S(407));n=n()}else{if(n=t(),b===null)throw Error(S(349));Dt&30||Ka(r,t,n)}l.memoizedState=n;var o={value:n,getSnapshot:t};return l.queue=o,Gi(Qa.bind(null,r,o,e),[e]),r.flags|=2048,rr(9,Wa.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=$e(),t=b.identifierPrefix;if($){var n=Je,r=Xe;n=(r&~(1<<32-De(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=tr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=qf++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},rp={readContext:Te,useCallback:ec,useContext:Te,useEffect:Vs,useImperativeHandle:ba,useInsertionEffect:Ja,useLayoutEffect:Za,useMemo:tc,useReducer:io,useRef:Xa,useState:function(){return io(nr)},useDebugValue:Ks,useDeferredValue:function(e){var t=Le();return nc(t,X.memoizedState,e)},useTransition:function(){var e=io(nr)[0],t=Le().memoizedState;return[e,t]},useMutableSource:Ba,useSyncExternalStore:Va,useId:rc,unstable_isNewReconciler:!1},lp={readContext:Te,useCallback:ec,useContext:Te,useEffect:Vs,useImperativeHandle:ba,useInsertionEffect:Ja,useLayoutEffect:Za,useMemo:tc,useReducer:uo,useRef:Xa,useState:function(){return uo(nr)},useDebugValue:Ks,useDeferredValue:function(e){var t=Le();return X===null?t.memoizedState=e:nc(t,X.memoizedState,e)},useTransition:function(){var e=uo(nr)[0],t=Le().memoizedState;return[e,t]},useMutableSource:Ba,useSyncExternalStore:Va,useId:rc,unstable_isNewReconciler:!1};function Oe(e,t){if(e&&e.defaultProps){t=V({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Qo(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:V({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var jl={isMounted:function(e){return(e=e._reactInternals)?$t(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ce(),l=vt(e),o=Ze(r,l);o.payload=t,n!=null&&(o.callback=n),t=ht(e,o,l),t!==null&&(Ae(t,e,l,r),Hr(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ce(),l=vt(e),o=Ze(r,l);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=ht(e,o,l),t!==null&&(Ae(t,e,l,r),Hr(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ce(),r=vt(e),l=Ze(n,r);l.tag=2,t!=null&&(l.callback=t),t=ht(e,l,r),t!==null&&(Ae(t,e,r,n),Hr(t,e,r))}};function Yi(e,t,n,r,l,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!Xn(n,r)||!Xn(l,o):!0}function ic(e,t,n){var r=!1,l=wt,o=t.contextType;return typeof o=="object"&&o!==null?o=Te(o):(l=ye(t)?Mt:ie.current,r=t.contextTypes,o=(r=r!=null)?un(e,l):wt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=jl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function Xi(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&jl.enqueueReplaceState(t,t.state,null)}function Go(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Ds(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=Te(o):(o=ye(t)?Mt:ie.current,l.context=un(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Qo(e,t,o,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&jl.enqueueReplaceState(l,l.state,null),cl(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function fn(e,t){try{var n="",r=t;do n+=Rd(r),r=r.return;while(r);var l=n}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function ao(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Yo(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var op=typeof WeakMap=="function"?WeakMap:Map;function uc(e,t,n){n=Ze(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){hl||(hl=!0,ls=r),Yo(e,t)},n}function ac(e,t,n){n=Ze(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){Yo(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Yo(e,t),typeof r!="function"&&(yt===null?yt=new Set([this]):yt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Ji(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new op;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=xp.bind(null,e,t,n),t.then(e,e))}function Zi(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function qi(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ze(-1,1),t.tag=2,ht(n,t,1))),n.lanes|=1),e)}var sp=rt.ReactCurrentOwner,me=!1;function ue(e,t,n,r){t.child=e===null?Fa(t,null,n,r):cn(t,e.child,n,r)}function bi(e,t,n,r,l){n=n.render;var o=t.ref;return ln(t,l),r=Hs(e,t,n,r,o,l),n=Bs(),e!==null&&!me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,nt(e,t,l)):($&&n&&Ts(t),t.flags|=1,ue(e,t,r,l),t.child)}function eu(e,t,n,r,l){if(e===null){var o=n.type;return typeof o=="function"&&!qs(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,cc(e,t,o,r,l)):(e=Gr(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&l)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:Xn,n(s,r)&&e.ref===t.ref)return nt(e,t,l)}return t.flags|=1,e=gt(o,r),e.ref=t.ref,e.return=t,t.child=e}function cc(e,t,n,r,l){if(e!==null){var o=e.memoizedProps;if(Xn(o,r)&&e.ref===t.ref)if(me=!1,t.pendingProps=r=o,(e.lanes&l)!==0)e.flags&131072&&(me=!0);else return t.lanes=e.lanes,nt(e,t,l)}return Xo(e,t,n,r,l)}function dc(e,t,n){var r=t.pendingProps,l=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},A(bt,ge),ge|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,A(bt,ge),ge|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,A(bt,ge),ge|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,A(bt,ge),ge|=r;return ue(e,t,l,n),t.child}function fc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Xo(e,t,n,r,l){var o=ye(n)?Mt:ie.current;return o=un(t,o),ln(t,l),n=Hs(e,t,n,r,o,l),r=Bs(),e!==null&&!me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,nt(e,t,l)):($&&r&&Ts(t),t.flags|=1,ue(e,t,n,l),t.child)}function tu(e,t,n,r,l){if(ye(n)){var o=!0;ol(t)}else o=!1;if(ln(t,l),t.stateNode===null)Kr(e,t),ic(t,n,r),Go(t,n,r,l),r=!0;else if(e===null){var s=t.stateNode,i=t.memoizedProps;s.props=i;var u=s.context,a=n.contextType;typeof a=="object"&&a!==null?a=Te(a):(a=ye(n)?Mt:ie.current,a=un(t,a));var m=n.getDerivedStateFromProps,y=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function";y||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(i!==r||u!==a)&&Xi(t,s,r,a),st=!1;var h=t.memoizedState;s.state=h,cl(t,r,s,l),u=t.memoizedState,i!==r||h!==u||he.current||st?(typeof m=="function"&&(Qo(t,n,m,r),u=t.memoizedState),(i=st||Yi(t,n,i,r,h,u,a))?(y||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),s.props=r,s.state=u,s.context=a,r=i):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,$a(e,t),i=t.memoizedProps,a=t.type===t.elementType?i:Oe(t.type,i),s.props=a,y=t.pendingProps,h=s.context,u=n.contextType,typeof u=="object"&&u!==null?u=Te(u):(u=ye(n)?Mt:ie.current,u=un(t,u));var g=n.getDerivedStateFromProps;(m=typeof g=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(i!==y||h!==u)&&Xi(t,s,r,u),st=!1,h=t.memoizedState,s.state=h,cl(t,r,s,l);var v=t.memoizedState;i!==y||h!==v||he.current||st?(typeof g=="function"&&(Qo(t,n,g,r),v=t.memoizedState),(a=st||Yi(t,n,a,r,h,v,u)||!1)?(m||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,v,u),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,v,u)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),s.props=r,s.state=v,s.context=u,r=a):(typeof s.componentDidUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return Jo(e,t,n,r,o,l)}function Jo(e,t,n,r,l,o){fc(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return l&&$i(t,n,!1),nt(e,t,o);r=t.stateNode,sp.current=t;var i=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=cn(t,e.child,null,o),t.child=cn(t,null,i,o)):ue(e,t,i,o),t.memoizedState=r.state,l&&$i(t,n,!0),t.child}function pc(e){var t=e.stateNode;t.pendingContext?Ui(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ui(e,t.context,!1),As(e,t.containerInfo)}function nu(e,t,n,r,l){return an(),Rs(l),t.flags|=256,ue(e,t,n,r),t.child}var Zo={dehydrated:null,treeContext:null,retryLane:0};function qo(e){return{baseLanes:e,cachePool:null,transitions:null}}function mc(e,t,n){var r=t.pendingProps,l=H.current,o=!1,s=(t.flags&128)!==0,i;if((i=s)||(i=e!==null&&e.memoizedState===null?!1:(l&2)!==0),i?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),A(H,l&1),e===null)return Ko(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Ll(s,r,0,null),e=Ot(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=qo(n),t.memoizedState=Zo,e):Ws(t,s));if(l=e.memoizedState,l!==null&&(i=l.dehydrated,i!==null))return ip(e,t,s,r,i,l,n);if(o){o=r.fallback,s=t.mode,l=e.child,i=l.sibling;var u={mode:"hidden",children:r.children};return!(s&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=gt(l,u),r.subtreeFlags=l.subtreeFlags&14680064),i!==null?o=gt(i,o):(o=Ot(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?qo(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=Zo,r}return o=e.child,e=o.sibling,r=gt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ws(e,t){return t=Ll({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Lr(e,t,n,r){return r!==null&&Rs(r),cn(t,e.child,null,n),e=Ws(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ip(e,t,n,r,l,o,s){if(n)return t.flags&256?(t.flags&=-257,r=ao(Error(S(422))),Lr(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,l=t.mode,r=Ll({mode:"visible",children:r.children},l,0,null),o=Ot(o,l,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&cn(t,e.child,null,s),t.child.memoizedState=qo(s),t.memoizedState=Zo,o);if(!(t.mode&1))return Lr(e,t,s,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var i=r.dgst;return r=i,o=Error(S(419)),r=ao(o,r,void 0),Lr(e,t,s,r)}if(i=(s&e.childLanes)!==0,me||i){if(r=b,r!==null){switch(s&-s){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|s)?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,tt(e,l),Ae(r,e,l,-1))}return Zs(),r=ao(Error(S(421))),Lr(e,t,s,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=wp.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,xe=mt(l.nextSibling),we=t,$=!0,Ie=null,e!==null&&(Ce[Ne++]=Xe,Ce[Ne++]=Je,Ce[Ne++]=It,Xe=e.id,Je=e.overflow,It=t),t=Ws(t,r.children),t.flags|=4096,t)}function ru(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Wo(e.return,t,n)}function co(e,t,n,r,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=l)}function hc(e,t,n){var r=t.pendingProps,l=r.revealOrder,o=r.tail;if(ue(e,t,r.children,n),r=H.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ru(e,n,t);else if(e.tag===19)ru(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(A(H,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&dl(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),co(t,!1,l,n,o);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&dl(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}co(t,!0,n,null,o);break;case"together":co(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Kr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function nt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),At|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(S(153));if(t.child!==null){for(e=t.child,n=gt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=gt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function up(e,t,n){switch(t.tag){case 3:pc(t),an();break;case 5:Ha(t);break;case 1:ye(t.type)&&ol(t);break;case 4:As(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;A(ul,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(A(H,H.current&1),t.flags|=128,null):n&t.child.childLanes?mc(e,t,n):(A(H,H.current&1),e=nt(e,t,n),e!==null?e.sibling:null);A(H,H.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return hc(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),A(H,H.current),r)break;return null;case 22:case 23:return t.lanes=0,dc(e,t,n)}return nt(e,t,n)}var yc,bo,vc,gc;yc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};bo=function(){};vc=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,Rt(Ve.current);var o=null;switch(n){case"input":l=So(e,l),r=So(e,r),o=[];break;case"select":l=V({},l,{value:void 0}),r=V({},r,{value:void 0}),o=[];break;case"textarea":l=Co(e,l),r=Co(e,r),o=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=rl)}_o(n,r);var s;n=null;for(a in l)if(!r.hasOwnProperty(a)&&l.hasOwnProperty(a)&&l[a]!=null)if(a==="style"){var i=l[a];for(s in i)i.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else a!=="dangerouslySetInnerHTML"&&a!=="children"&&a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&a!=="autoFocus"&&(Bn.hasOwnProperty(a)?o||(o=[]):(o=o||[]).push(a,null));for(a in r){var u=r[a];if(i=l!=null?l[a]:void 0,r.hasOwnProperty(a)&&u!==i&&(u!=null||i!=null))if(a==="style")if(i){for(s in i)!i.hasOwnProperty(s)||u&&u.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in u)u.hasOwnProperty(s)&&i[s]!==u[s]&&(n||(n={}),n[s]=u[s])}else n||(o||(o=[]),o.push(a,n)),n=u;else a==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,i=i?i.__html:void 0,u!=null&&i!==u&&(o=o||[]).push(a,u)):a==="children"?typeof u!="string"&&typeof u!="number"||(o=o||[]).push(a,""+u):a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&(Bn.hasOwnProperty(a)?(u!=null&&a==="onScroll"&&F("scroll",e),o||i===u||(o=[])):(o=o||[]).push(a,u))}n&&(o=o||[]).push("style",n);var a=o;(t.updateQueue=a)&&(t.flags|=4)}};gc=function(e,t,n,r){n!==r&&(t.flags|=4)};function jn(e,t){if(!$)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function oe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ap(e,t,n){var r=t.pendingProps;switch(Ls(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return oe(t),null;case 1:return ye(t.type)&&ll(),oe(t),null;case 3:return r=t.stateNode,dn(),U(he),U(ie),Us(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Pr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ie!==null&&(is(Ie),Ie=null))),bo(e,t),oe(t),null;case 5:Fs(t);var l=Rt(er.current);if(n=t.type,e!==null&&t.stateNode!=null)vc(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(S(166));return oe(t),null}if(e=Rt(Ve.current),Pr(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[He]=t,r[qn]=o,e=(t.mode&1)!==0,n){case"dialog":F("cancel",r),F("close",r);break;case"iframe":case"object":case"embed":F("load",r);break;case"video":case"audio":for(l=0;l<zn.length;l++)F(zn[l],r);break;case"source":F("error",r);break;case"img":case"image":case"link":F("error",r),F("load",r);break;case"details":F("toggle",r);break;case"input":fi(r,o),F("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},F("invalid",r);break;case"textarea":mi(r,o),F("invalid",r)}_o(n,o),l=null;for(var s in o)if(o.hasOwnProperty(s)){var i=o[s];s==="children"?typeof i=="string"?r.textContent!==i&&(o.suppressHydrationWarning!==!0&&jr(r.textContent,i,e),l=["children",i]):typeof i=="number"&&r.textContent!==""+i&&(o.suppressHydrationWarning!==!0&&jr(r.textContent,i,e),l=["children",""+i]):Bn.hasOwnProperty(s)&&i!=null&&s==="onScroll"&&F("scroll",r)}switch(n){case"input":xr(r),pi(r,o,!0);break;case"textarea":xr(r),hi(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=rl)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Qu(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[He]=t,e[qn]=r,yc(e,t,!1,!1),t.stateNode=e;e:{switch(s=jo(n,r),n){case"dialog":F("cancel",e),F("close",e),l=r;break;case"iframe":case"object":case"embed":F("load",e),l=r;break;case"video":case"audio":for(l=0;l<zn.length;l++)F(zn[l],e);l=r;break;case"source":F("error",e),l=r;break;case"img":case"image":case"link":F("error",e),F("load",e),l=r;break;case"details":F("toggle",e),l=r;break;case"input":fi(e,r),l=So(e,r),F("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=V({},r,{value:void 0}),F("invalid",e);break;case"textarea":mi(e,r),l=Co(e,r),F("invalid",e);break;default:l=r}_o(n,l),i=l;for(o in i)if(i.hasOwnProperty(o)){var u=i[o];o==="style"?Xu(e,u):o==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Gu(e,u)):o==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&Vn(e,u):typeof u=="number"&&Vn(e,""+u):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Bn.hasOwnProperty(o)?u!=null&&o==="onScroll"&&F("scroll",e):u!=null&&hs(e,o,u,s))}switch(n){case"input":xr(e),pi(e,r,!1);break;case"textarea":xr(e),hi(e);break;case"option":r.value!=null&&e.setAttribute("value",""+xt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?en(e,!!r.multiple,o,!1):r.defaultValue!=null&&en(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=rl)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return oe(t),null;case 6:if(e&&t.stateNode!=null)gc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(S(166));if(n=Rt(er.current),Rt(Ve.current),Pr(t)){if(r=t.stateNode,n=t.memoizedProps,r[He]=t,(o=r.nodeValue!==n)&&(e=we,e!==null))switch(e.tag){case 3:jr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&jr(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[He]=t,t.stateNode=r}return oe(t),null;case 13:if(U(H),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if($&&xe!==null&&t.mode&1&&!(t.flags&128))Da(),an(),t.flags|=98560,o=!1;else if(o=Pr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(S(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(S(317));o[He]=t}else an(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;oe(t),o=!1}else Ie!==null&&(is(Ie),Ie=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||H.current&1?J===0&&(J=3):Zs())),t.updateQueue!==null&&(t.flags|=4),oe(t),null);case 4:return dn(),bo(e,t),e===null&&Jn(t.stateNode.containerInfo),oe(t),null;case 10:return Ms(t.type._context),oe(t),null;case 17:return ye(t.type)&&ll(),oe(t),null;case 19:if(U(H),o=t.memoizedState,o===null)return oe(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)jn(o,!1);else{if(J!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=dl(e),s!==null){for(t.flags|=128,jn(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return A(H,H.current&1|2),t.child}e=e.sibling}o.tail!==null&&G()>pn&&(t.flags|=128,r=!0,jn(o,!1),t.lanes=4194304)}else{if(!r)if(e=dl(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),jn(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!$)return oe(t),null}else 2*G()-o.renderingStartTime>pn&&n!==1073741824&&(t.flags|=128,r=!0,jn(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=G(),t.sibling=null,n=H.current,A(H,r?n&1|2:n&1),t):(oe(t),null);case 22:case 23:return Js(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?ge&1073741824&&(oe(t),t.subtreeFlags&6&&(t.flags|=8192)):oe(t),null;case 24:return null;case 25:return null}throw Error(S(156,t.tag))}function cp(e,t){switch(Ls(t),t.tag){case 1:return ye(t.type)&&ll(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return dn(),U(he),U(ie),Us(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Fs(t),null;case 13:if(U(H),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(S(340));an()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return U(H),null;case 4:return dn(),null;case 10:return Ms(t.type._context),null;case 22:case 23:return Js(),null;case 24:return null;default:return null}}var Rr=!1,se=!1,dp=typeof WeakSet=="function"?WeakSet:Set,C=null;function qt(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){K(e,t,r)}else n.current=null}function es(e,t,n){try{n()}catch(r){K(e,t,r)}}var lu=!1;function fp(e,t){if(Ao=el,e=Ea(),Ps(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,i=-1,u=-1,a=0,m=0,y=e,h=null;t:for(;;){for(var g;y!==n||l!==0&&y.nodeType!==3||(i=s+l),y!==o||r!==0&&y.nodeType!==3||(u=s+r),y.nodeType===3&&(s+=y.nodeValue.length),(g=y.firstChild)!==null;)h=y,y=g;for(;;){if(y===e)break t;if(h===n&&++a===l&&(i=s),h===o&&++m===r&&(u=s),(g=y.nextSibling)!==null)break;y=h,h=y.parentNode}y=g}n=i===-1||u===-1?null:{start:i,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Fo={focusedElem:e,selectionRange:n},el=!1,C=t;C!==null;)if(t=C,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,C=e;else for(;C!==null;){t=C;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,k=v.memoizedState,d=t.stateNode,c=d.getSnapshotBeforeUpdate(t.elementType===t.type?x:Oe(t.type,x),k);d.__reactInternalSnapshotBeforeUpdate=c}break;case 3:var p=t.stateNode.containerInfo;p.nodeType===1?p.textContent="":p.nodeType===9&&p.documentElement&&p.removeChild(p.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(S(163))}}catch(w){K(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,C=e;break}C=t.return}return v=lu,lu=!1,v}function Un(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&es(t,n,o)}l=l.next}while(l!==r)}}function Pl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ts(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function xc(e){var t=e.alternate;t!==null&&(e.alternate=null,xc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[He],delete t[qn],delete t[Ho],delete t[Yf],delete t[Xf])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function wc(e){return e.tag===5||e.tag===3||e.tag===4}function ou(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||wc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ns(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=rl));else if(r!==4&&(e=e.child,e!==null))for(ns(e,t,n),e=e.sibling;e!==null;)ns(e,t,n),e=e.sibling}function rs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(rs(e,t,n),e=e.sibling;e!==null;)rs(e,t,n),e=e.sibling}var te=null,Me=!1;function lt(e,t,n){for(n=n.child;n!==null;)Sc(e,t,n),n=n.sibling}function Sc(e,t,n){if(Be&&typeof Be.onCommitFiberUnmount=="function")try{Be.onCommitFiberUnmount(wl,n)}catch{}switch(n.tag){case 5:se||qt(n,t);case 6:var r=te,l=Me;te=null,lt(e,t,n),te=r,Me=l,te!==null&&(Me?(e=te,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):te.removeChild(n.stateNode));break;case 18:te!==null&&(Me?(e=te,n=n.stateNode,e.nodeType===8?ro(e.parentNode,n):e.nodeType===1&&ro(e,n),Gn(e)):ro(te,n.stateNode));break;case 4:r=te,l=Me,te=n.stateNode.containerInfo,Me=!0,lt(e,t,n),te=r,Me=l;break;case 0:case 11:case 14:case 15:if(!se&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var o=l,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&es(n,t,s),l=l.next}while(l!==r)}lt(e,t,n);break;case 1:if(!se&&(qt(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){K(n,t,i)}lt(e,t,n);break;case 21:lt(e,t,n);break;case 22:n.mode&1?(se=(r=se)||n.memoizedState!==null,lt(e,t,n),se=r):lt(e,t,n);break;default:lt(e,t,n)}}function su(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new dp),t.forEach(function(r){var l=Sp.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function ze(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var o=e,s=t,i=s;e:for(;i!==null;){switch(i.tag){case 5:te=i.stateNode,Me=!1;break e;case 3:te=i.stateNode.containerInfo,Me=!0;break e;case 4:te=i.stateNode.containerInfo,Me=!0;break e}i=i.return}if(te===null)throw Error(S(160));Sc(o,s,l),te=null,Me=!1;var u=l.alternate;u!==null&&(u.return=null),l.return=null}catch(a){K(l,t,a)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)kc(t,e),t=t.sibling}function kc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ze(t,e),Ue(e),r&4){try{Un(3,e,e.return),Pl(3,e)}catch(x){K(e,e.return,x)}try{Un(5,e,e.return)}catch(x){K(e,e.return,x)}}break;case 1:ze(t,e),Ue(e),r&512&&n!==null&&qt(n,n.return);break;case 5:if(ze(t,e),Ue(e),r&512&&n!==null&&qt(n,n.return),e.flags&32){var l=e.stateNode;try{Vn(l,"")}catch(x){K(e,e.return,x)}}if(r&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,i=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{i==="input"&&o.type==="radio"&&o.name!=null&&Ku(l,o),jo(i,s);var a=jo(i,o);for(s=0;s<u.length;s+=2){var m=u[s],y=u[s+1];m==="style"?Xu(l,y):m==="dangerouslySetInnerHTML"?Gu(l,y):m==="children"?Vn(l,y):hs(l,m,y,a)}switch(i){case"input":ko(l,o);break;case"textarea":Wu(l,o);break;case"select":var h=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var g=o.value;g!=null?en(l,!!o.multiple,g,!1):h!==!!o.multiple&&(o.defaultValue!=null?en(l,!!o.multiple,o.defaultValue,!0):en(l,!!o.multiple,o.multiple?[]:"",!1))}l[qn]=o}catch(x){K(e,e.return,x)}}break;case 6:if(ze(t,e),Ue(e),r&4){if(e.stateNode===null)throw Error(S(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(x){K(e,e.return,x)}}break;case 3:if(ze(t,e),Ue(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Gn(t.containerInfo)}catch(x){K(e,e.return,x)}break;case 4:ze(t,e),Ue(e);break;case 13:ze(t,e),Ue(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(Ys=G())),r&4&&su(e);break;case 22:if(m=n!==null&&n.memoizedState!==null,e.mode&1?(se=(a=se)||m,ze(t,e),se=a):ze(t,e),Ue(e),r&8192){if(a=e.memoizedState!==null,(e.stateNode.isHidden=a)&&!m&&e.mode&1)for(C=e,m=e.child;m!==null;){for(y=C=m;C!==null;){switch(h=C,g=h.child,h.tag){case 0:case 11:case 14:case 15:Un(4,h,h.return);break;case 1:qt(h,h.return);var v=h.stateNode;if(typeof v.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){K(r,n,x)}}break;case 5:qt(h,h.return);break;case 22:if(h.memoizedState!==null){uu(y);continue}}g!==null?(g.return=h,C=g):uu(y)}m=m.sibling}e:for(m=null,y=e;;){if(y.tag===5){if(m===null){m=y;try{l=y.stateNode,a?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(i=y.stateNode,u=y.memoizedProps.style,s=u!=null&&u.hasOwnProperty("display")?u.display:null,i.style.display=Yu("display",s))}catch(x){K(e,e.return,x)}}}else if(y.tag===6){if(m===null)try{y.stateNode.nodeValue=a?"":y.memoizedProps}catch(x){K(e,e.return,x)}}else if((y.tag!==22&&y.tag!==23||y.memoizedState===null||y===e)&&y.child!==null){y.child.return=y,y=y.child;continue}if(y===e)break e;for(;y.sibling===null;){if(y.return===null||y.return===e)break e;m===y&&(m=null),y=y.return}m===y&&(m=null),y.sibling.return=y.return,y=y.sibling}}break;case 19:ze(t,e),Ue(e),r&4&&su(e);break;case 21:break;default:ze(t,e),Ue(e)}}function Ue(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(wc(n)){var r=n;break e}n=n.return}throw Error(S(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(Vn(l,""),r.flags&=-33);var o=ou(e);rs(e,o,l);break;case 3:case 4:var s=r.stateNode.containerInfo,i=ou(e);ns(e,i,s);break;default:throw Error(S(161))}}catch(u){K(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function pp(e,t,n){C=e,Ec(e)}function Ec(e,t,n){for(var r=(e.mode&1)!==0;C!==null;){var l=C,o=l.child;if(l.tag===22&&r){var s=l.memoizedState!==null||Rr;if(!s){var i=l.alternate,u=i!==null&&i.memoizedState!==null||se;i=Rr;var a=se;if(Rr=s,(se=u)&&!a)for(C=l;C!==null;)s=C,u=s.child,s.tag===22&&s.memoizedState!==null?au(l):u!==null?(u.return=s,C=u):au(l);for(;o!==null;)C=o,Ec(o),o=o.sibling;C=l,Rr=i,se=a}iu(e)}else l.subtreeFlags&8772&&o!==null?(o.return=l,C=o):iu(e)}}function iu(e){for(;C!==null;){var t=C;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:se||Pl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!se)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:Oe(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Wi(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Wi(t,s,n)}break;case 5:var i=t.stateNode;if(n===null&&t.flags&4){n=i;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var a=t.alternate;if(a!==null){var m=a.memoizedState;if(m!==null){var y=m.dehydrated;y!==null&&Gn(y)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(S(163))}se||t.flags&512&&ts(t)}catch(h){K(t,t.return,h)}}if(t===e){C=null;break}if(n=t.sibling,n!==null){n.return=t.return,C=n;break}C=t.return}}function uu(e){for(;C!==null;){var t=C;if(t===e){C=null;break}var n=t.sibling;if(n!==null){n.return=t.return,C=n;break}C=t.return}}function au(e){for(;C!==null;){var t=C;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Pl(4,t)}catch(u){K(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(u){K(t,l,u)}}var o=t.return;try{ts(t)}catch(u){K(t,o,u)}break;case 5:var s=t.return;try{ts(t)}catch(u){K(t,s,u)}}}catch(u){K(t,t.return,u)}if(t===e){C=null;break}var i=t.sibling;if(i!==null){i.return=t.return,C=i;break}C=t.return}}var mp=Math.ceil,ml=rt.ReactCurrentDispatcher,Qs=rt.ReactCurrentOwner,Pe=rt.ReactCurrentBatchConfig,M=0,b=null,Y=null,ne=0,ge=0,bt=kt(0),J=0,lr=null,At=0,Tl=0,Gs=0,$n=null,pe=null,Ys=0,pn=1/0,Qe=null,hl=!1,ls=null,yt=null,zr=!1,ct=null,yl=0,Hn=0,os=null,Wr=-1,Qr=0;function ce(){return M&6?G():Wr!==-1?Wr:Wr=G()}function vt(e){return e.mode&1?M&2&&ne!==0?ne&-ne:Zf.transition!==null?(Qr===0&&(Qr=ia()),Qr):(e=I,e!==0||(e=window.event,e=e===void 0?16:ma(e.type)),e):1}function Ae(e,t,n,r){if(50<Hn)throw Hn=0,os=null,Error(S(185));ir(e,n,r),(!(M&2)||e!==b)&&(e===b&&(!(M&2)&&(Tl|=n),J===4&&ut(e,ne)),ve(e,r),n===1&&M===0&&!(t.mode&1)&&(pn=G()+500,Nl&&Et()))}function ve(e,t){var n=e.callbackNode;Jd(e,t);var r=br(e,e===b?ne:0);if(r===0)n!==null&&gi(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&gi(n),t===1)e.tag===0?Jf(cu.bind(null,e)):Oa(cu.bind(null,e)),Qf(function(){!(M&6)&&Et()}),n=null;else{switch(ua(r)){case 1:n=ws;break;case 4:n=oa;break;case 16:n=qr;break;case 536870912:n=sa;break;default:n=qr}n=Rc(n,Cc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Cc(e,t){if(Wr=-1,Qr=0,M&6)throw Error(S(327));var n=e.callbackNode;if(on()&&e.callbackNode!==n)return null;var r=br(e,e===b?ne:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=vl(e,r);else{t=r;var l=M;M|=2;var o=_c();(b!==e||ne!==t)&&(Qe=null,pn=G()+500,zt(e,t));do try{vp();break}catch(i){Nc(e,i)}while(!0);Os(),ml.current=o,M=l,Y!==null?t=0:(b=null,ne=0,t=J)}if(t!==0){if(t===2&&(l=zo(e),l!==0&&(r=l,t=ss(e,l))),t===1)throw n=lr,zt(e,0),ut(e,r),ve(e,G()),n;if(t===6)ut(e,r);else{if(l=e.current.alternate,!(r&30)&&!hp(l)&&(t=vl(e,r),t===2&&(o=zo(e),o!==0&&(r=o,t=ss(e,o))),t===1))throw n=lr,zt(e,0),ut(e,r),ve(e,G()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(S(345));case 2:Pt(e,pe,Qe);break;case 3:if(ut(e,r),(r&130023424)===r&&(t=Ys+500-G(),10<t)){if(br(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){ce(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=$o(Pt.bind(null,e,pe,Qe),t);break}Pt(e,pe,Qe);break;case 4:if(ut(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var s=31-De(r);o=1<<s,s=t[s],s>l&&(l=s),r&=~o}if(r=l,r=G()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*mp(r/1960))-r,10<r){e.timeoutHandle=$o(Pt.bind(null,e,pe,Qe),r);break}Pt(e,pe,Qe);break;case 5:Pt(e,pe,Qe);break;default:throw Error(S(329))}}}return ve(e,G()),e.callbackNode===n?Cc.bind(null,e):null}function ss(e,t){var n=$n;return e.current.memoizedState.isDehydrated&&(zt(e,t).flags|=256),e=vl(e,t),e!==2&&(t=pe,pe=n,t!==null&&is(t)),e}function is(e){pe===null?pe=e:pe.push.apply(pe,e)}function hp(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],o=l.getSnapshot;l=l.value;try{if(!Fe(o(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ut(e,t){for(t&=~Gs,t&=~Tl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-De(t),r=1<<n;e[n]=-1,t&=~r}}function cu(e){if(M&6)throw Error(S(327));on();var t=br(e,0);if(!(t&1))return ve(e,G()),null;var n=vl(e,t);if(e.tag!==0&&n===2){var r=zo(e);r!==0&&(t=r,n=ss(e,r))}if(n===1)throw n=lr,zt(e,0),ut(e,t),ve(e,G()),n;if(n===6)throw Error(S(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Pt(e,pe,Qe),ve(e,G()),null}function Xs(e,t){var n=M;M|=1;try{return e(t)}finally{M=n,M===0&&(pn=G()+500,Nl&&Et())}}function Ft(e){ct!==null&&ct.tag===0&&!(M&6)&&on();var t=M;M|=1;var n=Pe.transition,r=I;try{if(Pe.transition=null,I=1,e)return e()}finally{I=r,Pe.transition=n,M=t,!(M&6)&&Et()}}function Js(){ge=bt.current,U(bt)}function zt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Wf(n)),Y!==null)for(n=Y.return;n!==null;){var r=n;switch(Ls(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ll();break;case 3:dn(),U(he),U(ie),Us();break;case 5:Fs(r);break;case 4:dn();break;case 13:U(H);break;case 19:U(H);break;case 10:Ms(r.type._context);break;case 22:case 23:Js()}n=n.return}if(b=e,Y=e=gt(e.current,null),ne=ge=t,J=0,lr=null,Gs=Tl=At=0,pe=$n=null,Lt!==null){for(t=0;t<Lt.length;t++)if(n=Lt[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=l,r.next=s}n.pending=r}Lt=null}return e}function Nc(e,t){do{var n=Y;try{if(Os(),Br.current=pl,fl){for(var r=B.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}fl=!1}if(Dt=0,q=X=B=null,Fn=!1,tr=0,Qs.current=null,n===null||n.return===null){J=1,lr=t,Y=null;break}e:{var o=e,s=n.return,i=n,u=t;if(t=ne,i.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var a=u,m=i,y=m.tag;if(!(m.mode&1)&&(y===0||y===11||y===15)){var h=m.alternate;h?(m.updateQueue=h.updateQueue,m.memoizedState=h.memoizedState,m.lanes=h.lanes):(m.updateQueue=null,m.memoizedState=null)}var g=Zi(s);if(g!==null){g.flags&=-257,qi(g,s,i,o,t),g.mode&1&&Ji(o,a,t),t=g,u=a;var v=t.updateQueue;if(v===null){var x=new Set;x.add(u),t.updateQueue=x}else v.add(u);break e}else{if(!(t&1)){Ji(o,a,t),Zs();break e}u=Error(S(426))}}else if($&&i.mode&1){var k=Zi(s);if(k!==null){!(k.flags&65536)&&(k.flags|=256),qi(k,s,i,o,t),Rs(fn(u,i));break e}}o=u=fn(u,i),J!==4&&(J=2),$n===null?$n=[o]:$n.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var d=uc(o,u,t);Ki(o,d);break e;case 1:i=u;var c=o.type,p=o.stateNode;if(!(o.flags&128)&&(typeof c.getDerivedStateFromError=="function"||p!==null&&typeof p.componentDidCatch=="function"&&(yt===null||!yt.has(p)))){o.flags|=65536,t&=-t,o.lanes|=t;var w=ac(o,i,t);Ki(o,w);break e}}o=o.return}while(o!==null)}Pc(n)}catch(E){t=E,Y===n&&n!==null&&(Y=n=n.return);continue}break}while(!0)}function _c(){var e=ml.current;return ml.current=pl,e===null?pl:e}function Zs(){(J===0||J===3||J===2)&&(J=4),b===null||!(At&268435455)&&!(Tl&268435455)||ut(b,ne)}function vl(e,t){var n=M;M|=2;var r=_c();(b!==e||ne!==t)&&(Qe=null,zt(e,t));do try{yp();break}catch(l){Nc(e,l)}while(!0);if(Os(),M=n,ml.current=r,Y!==null)throw Error(S(261));return b=null,ne=0,J}function yp(){for(;Y!==null;)jc(Y)}function vp(){for(;Y!==null&&!Hd();)jc(Y)}function jc(e){var t=Lc(e.alternate,e,ge);e.memoizedProps=e.pendingProps,t===null?Pc(e):Y=t,Qs.current=null}function Pc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=cp(n,t),n!==null){n.flags&=32767,Y=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{J=6,Y=null;return}}else if(n=ap(n,t,ge),n!==null){Y=n;return}if(t=t.sibling,t!==null){Y=t;return}Y=t=e}while(t!==null);J===0&&(J=5)}function Pt(e,t,n){var r=I,l=Pe.transition;try{Pe.transition=null,I=1,gp(e,t,n,r)}finally{Pe.transition=l,I=r}return null}function gp(e,t,n,r){do on();while(ct!==null);if(M&6)throw Error(S(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(S(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Zd(e,o),e===b&&(Y=b=null,ne=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||zr||(zr=!0,Rc(qr,function(){return on(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Pe.transition,Pe.transition=null;var s=I;I=1;var i=M;M|=4,Qs.current=null,fp(e,n),kc(n,e),Ff(Fo),el=!!Ao,Fo=Ao=null,e.current=n,pp(n),Bd(),M=i,I=s,Pe.transition=o}else e.current=n;if(zr&&(zr=!1,ct=e,yl=l),o=e.pendingLanes,o===0&&(yt=null),Wd(n.stateNode),ve(e,G()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(hl)throw hl=!1,e=ls,ls=null,e;return yl&1&&e.tag!==0&&on(),o=e.pendingLanes,o&1?e===os?Hn++:(Hn=0,os=e):Hn=0,Et(),null}function on(){if(ct!==null){var e=ua(yl),t=Pe.transition,n=I;try{if(Pe.transition=null,I=16>e?16:e,ct===null)var r=!1;else{if(e=ct,ct=null,yl=0,M&6)throw Error(S(331));var l=M;for(M|=4,C=e.current;C!==null;){var o=C,s=o.child;if(C.flags&16){var i=o.deletions;if(i!==null){for(var u=0;u<i.length;u++){var a=i[u];for(C=a;C!==null;){var m=C;switch(m.tag){case 0:case 11:case 15:Un(8,m,o)}var y=m.child;if(y!==null)y.return=m,C=y;else for(;C!==null;){m=C;var h=m.sibling,g=m.return;if(xc(m),m===a){C=null;break}if(h!==null){h.return=g,C=h;break}C=g}}}var v=o.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var k=x.sibling;x.sibling=null,x=k}while(x!==null)}}C=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,C=s;else e:for(;C!==null;){if(o=C,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Un(9,o,o.return)}var d=o.sibling;if(d!==null){d.return=o.return,C=d;break e}C=o.return}}var c=e.current;for(C=c;C!==null;){s=C;var p=s.child;if(s.subtreeFlags&2064&&p!==null)p.return=s,C=p;else e:for(s=c;C!==null;){if(i=C,i.flags&2048)try{switch(i.tag){case 0:case 11:case 15:Pl(9,i)}}catch(E){K(i,i.return,E)}if(i===s){C=null;break e}var w=i.sibling;if(w!==null){w.return=i.return,C=w;break e}C=i.return}}if(M=l,Et(),Be&&typeof Be.onPostCommitFiberRoot=="function")try{Be.onPostCommitFiberRoot(wl,e)}catch{}r=!0}return r}finally{I=n,Pe.transition=t}}return!1}function du(e,t,n){t=fn(n,t),t=uc(e,t,1),e=ht(e,t,1),t=ce(),e!==null&&(ir(e,1,t),ve(e,t))}function K(e,t,n){if(e.tag===3)du(e,e,n);else for(;t!==null;){if(t.tag===3){du(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(yt===null||!yt.has(r))){e=fn(n,e),e=ac(t,e,1),t=ht(t,e,1),e=ce(),t!==null&&(ir(t,1,e),ve(t,e));break}}t=t.return}}function xp(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ce(),e.pingedLanes|=e.suspendedLanes&n,b===e&&(ne&n)===n&&(J===4||J===3&&(ne&130023424)===ne&&500>G()-Ys?zt(e,0):Gs|=n),ve(e,t)}function Tc(e,t){t===0&&(e.mode&1?(t=kr,kr<<=1,!(kr&130023424)&&(kr=4194304)):t=1);var n=ce();e=tt(e,t),e!==null&&(ir(e,t,n),ve(e,n))}function wp(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Tc(e,n)}function Sp(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(S(314))}r!==null&&r.delete(t),Tc(e,n)}var Lc;Lc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||he.current)me=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return me=!1,up(e,t,n);me=!!(e.flags&131072)}else me=!1,$&&t.flags&1048576&&Ma(t,il,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Kr(e,t),e=t.pendingProps;var l=un(t,ie.current);ln(t,n),l=Hs(null,t,r,e,l,n);var o=Bs();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ye(r)?(o=!0,ol(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Ds(t),l.updater=jl,t.stateNode=l,l._reactInternals=t,Go(t,r,e,n),t=Jo(null,t,r,!0,o,n)):(t.tag=0,$&&o&&Ts(t),ue(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Kr(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=Ep(r),e=Oe(r,e),l){case 0:t=Xo(null,t,r,e,n);break e;case 1:t=tu(null,t,r,e,n);break e;case 11:t=bi(null,t,r,e,n);break e;case 14:t=eu(null,t,r,Oe(r.type,e),n);break e}throw Error(S(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Oe(r,l),Xo(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Oe(r,l),tu(e,t,r,l,n);case 3:e:{if(pc(t),e===null)throw Error(S(387));r=t.pendingProps,o=t.memoizedState,l=o.element,$a(e,t),cl(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=fn(Error(S(423)),t),t=nu(e,t,r,n,l);break e}else if(r!==l){l=fn(Error(S(424)),t),t=nu(e,t,r,n,l);break e}else for(xe=mt(t.stateNode.containerInfo.firstChild),we=t,$=!0,Ie=null,n=Fa(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(an(),r===l){t=nt(e,t,n);break e}ue(e,t,r,n)}t=t.child}return t;case 5:return Ha(t),e===null&&Ko(t),r=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,s=l.children,Uo(r,l)?s=null:o!==null&&Uo(r,o)&&(t.flags|=32),fc(e,t),ue(e,t,s,n),t.child;case 6:return e===null&&Ko(t),null;case 13:return mc(e,t,n);case 4:return As(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=cn(t,null,r,n):ue(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Oe(r,l),bi(e,t,r,l,n);case 7:return ue(e,t,t.pendingProps,n),t.child;case 8:return ue(e,t,t.pendingProps.children,n),t.child;case 12:return ue(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,o=t.memoizedProps,s=l.value,A(ul,r._currentValue),r._currentValue=s,o!==null)if(Fe(o.value,s)){if(o.children===l.children&&!he.current){t=nt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var i=o.dependencies;if(i!==null){s=o.child;for(var u=i.firstContext;u!==null;){if(u.context===r){if(o.tag===1){u=Ze(-1,n&-n),u.tag=2;var a=o.updateQueue;if(a!==null){a=a.shared;var m=a.pending;m===null?u.next=u:(u.next=m.next,m.next=u),a.pending=u}}o.lanes|=n,u=o.alternate,u!==null&&(u.lanes|=n),Wo(o.return,n,t),i.lanes|=n;break}u=u.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(S(341));s.lanes|=n,i=s.alternate,i!==null&&(i.lanes|=n),Wo(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}ue(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,ln(t,n),l=Te(l),r=r(l),t.flags|=1,ue(e,t,r,n),t.child;case 14:return r=t.type,l=Oe(r,t.pendingProps),l=Oe(r.type,l),eu(e,t,r,l,n);case 15:return cc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Oe(r,l),Kr(e,t),t.tag=1,ye(r)?(e=!0,ol(t)):e=!1,ln(t,n),ic(t,r,l),Go(t,r,l,n),Jo(null,t,r,!0,e,n);case 19:return hc(e,t,n);case 22:return dc(e,t,n)}throw Error(S(156,t.tag))};function Rc(e,t){return la(e,t)}function kp(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function je(e,t,n,r){return new kp(e,t,n,r)}function qs(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ep(e){if(typeof e=="function")return qs(e)?1:0;if(e!=null){if(e=e.$$typeof,e===vs)return 11;if(e===gs)return 14}return 2}function gt(e,t){var n=e.alternate;return n===null?(n=je(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Gr(e,t,n,r,l,o){var s=2;if(r=e,typeof e=="function")qs(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Vt:return Ot(n.children,l,o,t);case ys:s=8,l|=8;break;case vo:return e=je(12,n,t,l|2),e.elementType=vo,e.lanes=o,e;case go:return e=je(13,n,t,l),e.elementType=go,e.lanes=o,e;case xo:return e=je(19,n,t,l),e.elementType=xo,e.lanes=o,e;case Hu:return Ll(n,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Uu:s=10;break e;case $u:s=9;break e;case vs:s=11;break e;case gs:s=14;break e;case ot:s=16,r=null;break e}throw Error(S(130,e==null?e:typeof e,""))}return t=je(s,n,t,l),t.elementType=e,t.type=r,t.lanes=o,t}function Ot(e,t,n,r){return e=je(7,e,r,t),e.lanes=n,e}function Ll(e,t,n,r){return e=je(22,e,r,t),e.elementType=Hu,e.lanes=n,e.stateNode={isHidden:!1},e}function fo(e,t,n){return e=je(6,e,null,t),e.lanes=n,e}function po(e,t,n){return t=je(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Cp(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ql(0),this.expirationTimes=Ql(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ql(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function bs(e,t,n,r,l,o,s,i,u){return e=new Cp(e,t,n,i,u),t===1?(t=1,o===!0&&(t|=8)):t=0,o=je(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ds(o),e}function Np(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Bt,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function zc(e){if(!e)return wt;e=e._reactInternals;e:{if($t(e)!==e||e.tag!==1)throw Error(S(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ye(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(S(171))}if(e.tag===1){var n=e.type;if(ye(n))return za(e,n,t)}return t}function Oc(e,t,n,r,l,o,s,i,u){return e=bs(n,r,!0,e,l,o,s,i,u),e.context=zc(null),n=e.current,r=ce(),l=vt(n),o=Ze(r,l),o.callback=t??null,ht(n,o,l),e.current.lanes=l,ir(e,l,r),ve(e,r),e}function Rl(e,t,n,r){var l=t.current,o=ce(),s=vt(l);return n=zc(n),t.context===null?t.context=n:t.pendingContext=n,t=Ze(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=ht(l,t,s),e!==null&&(Ae(e,l,s,o),Hr(e,l,s)),s}function gl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function fu(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ei(e,t){fu(e,t),(e=e.alternate)&&fu(e,t)}function _p(){return null}var Mc=typeof reportError=="function"?reportError:function(e){console.error(e)};function ti(e){this._internalRoot=e}zl.prototype.render=ti.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(S(409));Rl(e,t,null,null)};zl.prototype.unmount=ti.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Ft(function(){Rl(null,e,null,null)}),t[et]=null}};function zl(e){this._internalRoot=e}zl.prototype.unstable_scheduleHydration=function(e){if(e){var t=da();e={blockedOn:null,target:e,priority:t};for(var n=0;n<it.length&&t!==0&&t<it[n].priority;n++);it.splice(n,0,e),n===0&&pa(e)}};function ni(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ol(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function pu(){}function jp(e,t,n,r,l){if(l){if(typeof r=="function"){var o=r;r=function(){var a=gl(s);o.call(a)}}var s=Oc(t,r,e,0,null,!1,!1,"",pu);return e._reactRootContainer=s,e[et]=s.current,Jn(e.nodeType===8?e.parentNode:e),Ft(),s}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var i=r;r=function(){var a=gl(u);i.call(a)}}var u=bs(e,0,!1,null,null,!1,!1,"",pu);return e._reactRootContainer=u,e[et]=u.current,Jn(e.nodeType===8?e.parentNode:e),Ft(function(){Rl(t,u,n,r)}),u}function Ml(e,t,n,r,l){var o=n._reactRootContainer;if(o){var s=o;if(typeof l=="function"){var i=l;l=function(){var u=gl(s);i.call(u)}}Rl(t,s,e,l)}else s=jp(n,t,e,l,r);return gl(s)}aa=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Rn(t.pendingLanes);n!==0&&(Ss(t,n|1),ve(t,G()),!(M&6)&&(pn=G()+500,Et()))}break;case 13:Ft(function(){var r=tt(e,1);if(r!==null){var l=ce();Ae(r,e,1,l)}}),ei(e,1)}};ks=function(e){if(e.tag===13){var t=tt(e,134217728);if(t!==null){var n=ce();Ae(t,e,134217728,n)}ei(e,134217728)}};ca=function(e){if(e.tag===13){var t=vt(e),n=tt(e,t);if(n!==null){var r=ce();Ae(n,e,t,r)}ei(e,t)}};da=function(){return I};fa=function(e,t){var n=I;try{return I=e,t()}finally{I=n}};To=function(e,t,n){switch(t){case"input":if(ko(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=Cl(r);if(!l)throw Error(S(90));Vu(r),ko(r,l)}}}break;case"textarea":Wu(e,n);break;case"select":t=n.value,t!=null&&en(e,!!n.multiple,t,!1)}};qu=Xs;bu=Ft;var Pp={usingClientEntryPoint:!1,Events:[ar,Gt,Cl,Ju,Zu,Xs]},Pn={findFiberByHostInstance:Tt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Tp={bundleType:Pn.bundleType,version:Pn.version,rendererPackageName:Pn.rendererPackageName,rendererConfig:Pn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:rt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=na(e),e===null?null:e.stateNode},findFiberByHostInstance:Pn.findFiberByHostInstance||_p,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Or=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Or.isDisabled&&Or.supportsFiber)try{wl=Or.inject(Tp),Be=Or}catch{}}ke.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Pp;ke.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ni(t))throw Error(S(200));return Np(e,t,null,n)};ke.createRoot=function(e,t){if(!ni(e))throw Error(S(299));var n=!1,r="",l=Mc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=bs(e,1,!1,null,null,n,!1,r,l),e[et]=t.current,Jn(e.nodeType===8?e.parentNode:e),new ti(t)};ke.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(S(188)):(e=Object.keys(e).join(","),Error(S(268,e)));return e=na(t),e=e===null?null:e.stateNode,e};ke.flushSync=function(e){return Ft(e)};ke.hydrate=function(e,t,n){if(!Ol(t))throw Error(S(200));return Ml(null,e,t,!0,n)};ke.hydrateRoot=function(e,t,n){if(!ni(e))throw Error(S(405));var r=n!=null&&n.hydratedSources||null,l=!1,o="",s=Mc;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Oc(t,null,e,1,n??null,l,!1,o,s),e[et]=t.current,Jn(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new zl(t)};ke.render=function(e,t,n){if(!Ol(t))throw Error(S(200));return Ml(null,e,t,!1,n)};ke.unmountComponentAtNode=function(e){if(!Ol(e))throw Error(S(40));return e._reactRootContainer?(Ft(function(){Ml(null,null,e,!1,function(){e._reactRootContainer=null,e[et]=null})}),!0):!1};ke.unstable_batchedUpdates=Xs;ke.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ol(n))throw Error(S(200));if(e==null||e._reactInternals===void 0)throw Error(S(38));return Ml(e,t,n,!1,r)};ke.version="18.3.1-next-f1338f8080-20240426";function Ic(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Ic)}catch(e){console.error(e)}}Ic(),Iu.exports=ke;var Lp=Iu.exports,mu=Lp;ho.createRoot=mu.createRoot,ho.hydrateRoot=mu.hydrateRoot;/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rp=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Dc=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var zp={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Op=T.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:l="",children:o,iconNode:s,...i},u)=>T.createElement("svg",{ref:u,...zp,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Dc("lucide",l),...i},[...s.map(([a,m])=>T.createElement(a,m)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=(e,t)=>{const n=T.forwardRef(({className:r,...l},o)=>T.createElement(Op,{ref:o,iconNode:t,className:Dc(`lucide-${Rp(e)}`,r),...l}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mp=D("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ri=D("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ip=D("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dp=D("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const li=D("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ac=D("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ap=D("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fp=D("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Up=D("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $p=D("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hp=D("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bp=D("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _e=D("KeyRound",[["path",{d:"M2 18v3c0 .6.4 1 1 1h4v-3h3v-3h2l1.4-1.4a6.5 6.5 0 1 0-4-4Z",key:"167ctg"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dr=D("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vp=D("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kp=D("Paperclip",[["path",{d:"m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48",key:"1u3ebp"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wp=D("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fc=D("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uc=D("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qp=D("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gp=D("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $c=D("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yp=D("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xp=D("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hc=D("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bc=D("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jp=D("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vc=D("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Zp={},hu=e=>{let t;const n=new Set,r=(m,y)=>{const h=typeof m=="function"?m(t):m;if(!Object.is(h,t)){const g=t;t=y??(typeof h!="object"||h===null)?h:Object.assign({},t,h),n.forEach(v=>v(t,g))}},l=()=>t,u={setState:r,getState:l,getInitialState:()=>a,subscribe:m=>(n.add(m),()=>n.delete(m)),destroy:()=>{(Zp?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},a=t=e(r,l,u);return u},qp=e=>e?hu(e):hu;var Kc={exports:{}},Wc={},Qc={exports:{}},Gc={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mn=T;function bp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var em=typeof Object.is=="function"?Object.is:bp,tm=mn.useState,nm=mn.useEffect,rm=mn.useLayoutEffect,lm=mn.useDebugValue;function om(e,t){var n=t(),r=tm({inst:{value:n,getSnapshot:t}}),l=r[0].inst,o=r[1];return rm(function(){l.value=n,l.getSnapshot=t,mo(l)&&o({inst:l})},[e,n,t]),nm(function(){return mo(l)&&o({inst:l}),e(function(){mo(l)&&o({inst:l})})},[e]),lm(n),n}function mo(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!em(e,n)}catch{return!0}}function sm(e,t){return t()}var im=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?sm:om;Gc.useSyncExternalStore=mn.useSyncExternalStore!==void 0?mn.useSyncExternalStore:im;Qc.exports=Gc;var um=Qc.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Il=T,am=um;function cm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var dm=typeof Object.is=="function"?Object.is:cm,fm=am.useSyncExternalStore,pm=Il.useRef,mm=Il.useEffect,hm=Il.useMemo,ym=Il.useDebugValue;Wc.useSyncExternalStoreWithSelector=function(e,t,n,r,l){var o=pm(null);if(o.current===null){var s={hasValue:!1,value:null};o.current=s}else s=o.current;o=hm(function(){function u(g){if(!a){if(a=!0,m=g,g=r(g),l!==void 0&&s.hasValue){var v=s.value;if(l(v,g))return y=v}return y=g}if(v=y,dm(m,g))return v;var x=r(g);return l!==void 0&&l(v,x)?(m=g,v):(m=g,y=x)}var a=!1,m,y,h=n===void 0?null:n;return[function(){return u(t())},h===null?void 0:function(){return u(h())}]},[t,n,r,l]);var i=fm(e,o[0],o[1]);return mm(function(){s.hasValue=!0,s.value=i},[i]),ym(i),i};Kc.exports=Wc;var vm=Kc.exports;const gm=ku(vm),Yc={},{useDebugValue:xm}=Ou,{useSyncExternalStoreWithSelector:wm}=gm;let yu=!1;const Sm=e=>e;function km(e,t=Sm,n){(Yc?"production":void 0)!=="production"&&n&&!yu&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),yu=!0);const r=wm(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return xm(r),r}const vu=e=>{(Yc?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?qp(e):e,n=(r,l)=>km(t,r,l);return Object.assign(n,t),n},Dl=e=>e?vu(e):vu,fr=Dl(e=>({activeModal:null,modalData:null,openModal:(t,n=null)=>e({activeModal:t,modalData:n}),closeModal:()=>e({activeModal:null,modalData:null})})),Em={};function Cm(e,t){let n;try{n=e()}catch{return}return{getItem:l=>{var o;const s=u=>u===null?null:JSON.parse(u,void 0),i=(o=n.getItem(l))!=null?o:null;return i instanceof Promise?i.then(s):s(i)},setItem:(l,o)=>n.setItem(l,JSON.stringify(o,void 0)),removeItem:l=>n.removeItem(l)}}const or=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return or(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return or(r)(n)}}}},Nm=(e,t)=>(n,r,l)=>{let o={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:k=>k,version:0,merge:(k,d)=>({...d,...k}),...t},s=!1;const i=new Set,u=new Set;let a;try{a=o.getStorage()}catch{}if(!a)return e((...k)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),n(...k)},r,l);const m=or(o.serialize),y=()=>{const k=o.partialize({...r()});let d;const c=m({state:k,version:o.version}).then(p=>a.setItem(o.name,p)).catch(p=>{d=p});if(d)throw d;return c},h=l.setState;l.setState=(k,d)=>{h(k,d),y()};const g=e((...k)=>{n(...k),y()},r,l);let v;const x=()=>{var k;if(!a)return;s=!1,i.forEach(c=>c(r()));const d=((k=o.onRehydrateStorage)==null?void 0:k.call(o,r()))||void 0;return or(a.getItem.bind(a))(o.name).then(c=>{if(c)return o.deserialize(c)}).then(c=>{if(c)if(typeof c.version=="number"&&c.version!==o.version){if(o.migrate)return o.migrate(c.state,c.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return c.state}).then(c=>{var p;return v=o.merge(c,(p=r())!=null?p:g),n(v,!0),y()}).then(()=>{d==null||d(v,void 0),s=!0,u.forEach(c=>c(v))}).catch(c=>{d==null||d(void 0,c)})};return l.persist={setOptions:k=>{o={...o,...k},k.getStorage&&(a=k.getStorage())},clearStorage:()=>{a==null||a.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>x(),hasHydrated:()=>s,onHydrate:k=>(i.add(k),()=>{i.delete(k)}),onFinishHydration:k=>(u.add(k),()=>{u.delete(k)})},x(),v||g},_m=(e,t)=>(n,r,l)=>{let o={storage:Cm(()=>localStorage),partialize:x=>x,version:0,merge:(x,k)=>({...k,...x}),...t},s=!1;const i=new Set,u=new Set;let a=o.storage;if(!a)return e((...x)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),n(...x)},r,l);const m=()=>{const x=o.partialize({...r()});return a.setItem(o.name,{state:x,version:o.version})},y=l.setState;l.setState=(x,k)=>{y(x,k),m()};const h=e((...x)=>{n(...x),m()},r,l);l.getInitialState=()=>h;let g;const v=()=>{var x,k;if(!a)return;s=!1,i.forEach(c=>{var p;return c((p=r())!=null?p:h)});const d=((k=o.onRehydrateStorage)==null?void 0:k.call(o,(x=r())!=null?x:h))||void 0;return or(a.getItem.bind(a))(o.name).then(c=>{if(c)if(typeof c.version=="number"&&c.version!==o.version){if(o.migrate)return[!0,o.migrate(c.state,c.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,c.state];return[!1,void 0]}).then(c=>{var p;const[w,E]=c;if(g=o.merge(E,(p=r())!=null?p:h),n(g,!0),w)return m()}).then(()=>{d==null||d(g,void 0),g=r(),s=!0,u.forEach(c=>c(g))}).catch(c=>{d==null||d(void 0,c)})};return l.persist={setOptions:x=>{o={...o,...x},x.storage&&(a=x.storage)},clearStorage:()=>{a==null||a.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>v(),hasHydrated:()=>s,onHydrate:x=>(i.add(x),()=>{i.delete(x)}),onFinishHydration:x=>(u.add(x),()=>{u.delete(x)})},o.skipHydration||v(),g||h},jm=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((Em?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),Nm(e,t)):_m(e,t),Xc=jm;let Mr;const Pm=new Uint8Array(16);function Tm(){if(!Mr&&(Mr=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Mr))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Mr(Pm)}const ee=[];for(let e=0;e<256;++e)ee.push((e+256).toString(16).slice(1));function Lm(e,t=0){return ee[e[t+0]]+ee[e[t+1]]+ee[e[t+2]]+ee[e[t+3]]+"-"+ee[e[t+4]]+ee[e[t+5]]+"-"+ee[e[t+6]]+ee[e[t+7]]+"-"+ee[e[t+8]]+ee[e[t+9]]+"-"+ee[e[t+10]]+ee[e[t+11]]+ee[e[t+12]]+ee[e[t+13]]+ee[e[t+14]]+ee[e[t+15]]}const Rm=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),gu={randomUUID:Rm};function Jc(e,t,n){if(gu.randomUUID&&!e)return gu.randomUUID();e=e||{};const r=e.random||(e.rng||Tm)();return r[6]=r[6]&15|64,r[8]=r[8]&63|128,Lm(r)}const Ct=Dl(e=>({toasts:[],addToast:t=>{const n={...t,id:Date.now()};e(r=>({toasts:[...r.toasts,n]})),setTimeout(()=>{e(r=>({toasts:r.toasts.filter(l=>l.id!==n.id)}))},t.duration||5e3)},removeToast:t=>{e(n=>({toasts:n.toasts.filter(r=>r.id!==t)}))}})),ae={THEME_BRIGHTNESS_THRESHOLD:8421504,DEFAULT_OLLAMA_URL:"http://localhost:11434",OPENROUTER_REFERRER_URL:"https://sahai.com/cep",OPENROUTER_APP_TITLE:"SahAI CEP Extension",HEALTH_CHECK_INTERVAL_MS:3e4,MODEL_CACHE_DURATION_MS:36e5,DARK_THEME:{BG_COLOR:"#323232",TEXT_COLOR:"#F0F0F0",SECONDARY_BG_COLOR:"#3C3C3C",BORDER_COLOR:"#4A4A4A",SCROLLBAR_THUMB_COLOR:"#555555",SCROLLBAR_TRACK_COLOR:"#323232"},LIGHT_THEME:{BG_COLOR:"#F5F5F5",TEXT_COLOR:"#1a1a1a",SECONDARY_BG_COLOR:"#EAEAEA",BORDER_COLOR:"#D3D3D3",SCROLLBAR_THUMB_COLOR:"#C1C1C1",SCROLLBAR_TRACK_COLOR:"#F5F5F5"},ENDPOINTS:{OPENAI:"https://api.openai.com/v1",GROQ:"https://api.groq.com/openai/v1",DEEPSEEK:"https://api.deepseek.com",ANTHROPIC:"https://api.anthropic.com/v1",GOOGLE_GEMINI:"https://generativelanguage.googleapis.com/v1beta/models",OPENROUTER:"https://openrouter.ai/api/v1"},STORAGE_KEYS:{SETTINGS:"sahai-settings-storage"}},Ke={log:(...e)=>{},warn:(...e)=>{},error:(...e)=>{console.error(...e)},debug:(...e)=>{},info:(...e)=>{}},Zc={openai:{models:[]},anthropic:{models:[]},google:{models:[]},groq:{models:[]},deepseek:{models:[]},openrouter:{models:[]},ollama:{baseURL:"http://localhost:11434",models:[]}},We=Dl()(Xc((e,t)=>({providers:Zc,selectedProvider:"openai",selectedModel:"",theme:"auto",adobeTheme:null,modelCache:new Map,setProviderApiKey:(n,r)=>{us(n,r),t().refreshProviderModels(n)},setOllamaBaseUrl:n=>{e(r=>({providers:{...r.providers,ollama:{...r.providers.ollama,baseURL:n}}}))},setSelectedProvider:n=>{e({selectedProvider:n,selectedModel:""}),t().providers[n].models.length===0&&t().refreshProviderModels(n)},setSelectedModel:n=>e({selectedModel:n}),setTheme:(n,r)=>e({theme:n,...r&&{adobeTheme:r}}),applyTheme:()=>{const{theme:n,adobeTheme:r}=t(),l=document.documentElement;if(n==="auto"&&r||n!=="auto"){const s=n==="dark"||r&&parseInt(r.backgroundColor.substring(1),16)<ae.THEME_BRIGHTNESS_THRESHOLD?ae.DARK_THEME:ae.LIGHT_THEME;l.style.setProperty("--adobe-bg-color",s.BG_COLOR),l.style.setProperty("--adobe-text-color",s.TEXT_COLOR),l.style.setProperty("--adobe-secondary-bg-color",s.SECONDARY_BG_COLOR),l.style.setProperty("--adobe-border-color",s.BORDER_COLOR),l.style.setProperty("--adobe-scrollbar-thumb-color",s.SCROLLBAR_THUMB_COLOR),l.style.setProperty("--adobe-scrollbar-track-color",s.SCROLLBAR_TRACK_COLOR)}},refreshProviderModels:async n=>{const{modelCache:r}=t(),l=r.get(n);if(l&&Date.now()-l.timestamp<ae.MODEL_CACHE_DURATION_MS){e(o=>({providers:{...o.providers,[n]:{...o.providers[n],models:l.models}}}));return}try{Ke.log(`Fetching fresh models for ${n}`);const s=await pr(n).getModels();e(i=>({providers:{...i.providers,[n]:{...i.providers[n],models:s}},modelCache:r.set(n,{models:s,timestamp:Date.now()})})),!t().selectedModel&&s.length>0&&t().setSelectedModel(s[0].id)}catch(o){Ke.error(`Failed to fetch models for ${n}:`,o),Ct.getState().addToast({message:`Could not fetch models for ${n}. Check API key and connection.`,type:"error"}),e(s=>({providers:{...s.providers,[n]:{...s.providers[n],models:[]}}}))}}}),{name:ae.STORAGE_KEYS.SETTINGS,partialize:e=>({selectedProvider:e.selectedProvider,selectedModel:e.selectedModel,theme:e.theme,providers:{ollama:e.providers.ollama}})})),zm=()=>{Object.keys(Zc).forEach(e=>{qe(e)})};setTimeout(zm,0);const Om=Object.freeze(Object.defineProperty({__proto__:null,useSettingsStore:We},Symbol.toStringTag,{value:"Module"}));let Ye;const Mm=()=>{if(typeof CSInterface<"u")try{Ye=new CSInterface,setTimeout(()=>{qc()},100)}catch{Ye=null}},Im=e=>new Promise((t,n)=>{if(!Ye){n("Not in a CEP environment.");return}Ye.evalScript(e,r=>{try{t(JSON.parse(r))}catch{t(r)}})}),qc=()=>{var e,t,n;if(Ye)try{if(typeof Ye.getAppSkinInfo!="function"){Ke.warn("getAppSkinInfo method not available on CSInterface");return}const r=Ye.getAppSkinInfo();if(!r){Ke.warn("No theme information received from CSInterface");return}const l=(i,u,a)=>(i<<16|u<<8|a).toString(16).padStart(6,"0"),o={baseFontFamily:((e=r.baseFont)==null?void 0:e.family)||"Arial",baseFontSize:((t=r.baseFont)==null?void 0:t.size)||"12px",baseFontColor:(n=r.baseFont)!=null&&n.color?`#${l(r.baseFont.color.red,r.baseFont.color.green,r.baseFont.color.blue)}`:"#000000",backgroundColor:r.panelBackgroundColor?`#${l(r.panelBackgroundColor.red,r.panelBackgroundColor.green,r.panelBackgroundColor.blue)}`:"#F5F5F5"},s=Dm(o.backgroundColor);We.getState().setTheme(s,o),typeof Ye.addEventListener=="function"&&Ye.addEventListener("com.adobe.csxs.events.ThemeColorChanged",()=>qc())}catch(r){Ke.error("Error syncing theme:",r)}},Dm=e=>{const t=e.substring(1),n=parseInt(t.substring(0,2),16),r=parseInt(t.substring(2,4),16),l=parseInt(t.substring(4,6),16);return(n*299+r*587+l*114)/1e3>125?"light":"dark"},us=(e,t)=>{try{const n=btoa(t);localStorage.setItem(`sahai_api_key_${e}`,n)}catch(n){console.error("Failed to store credential:",n)}},qe=e=>{try{const t=localStorage.getItem(`sahai_api_key_${e}`);return t?atob(t):null}catch(t){return console.error("Failed to retrieve credential:",t),null}};class Yr{constructor(t,n){this.providerId=t,this.baseUrl=n}async getModels(){if(!qe(this.providerId))return[];const n=await this.makeRequest("/models");return(n.data||n).map(l=>({id:l.id,name:l.id,description:`Owned by ${l.owned_by||"unknown"}`,contextLength:l.context_window||l.context_length})).sort((l,o)=>l.name.localeCompare(o.name))}async*chat(t,n){if(!qe(this.providerId))throw new Error(`API key for ${this.providerId} not found.`);const l=JSON.stringify({model:n,messages:t.map(({role:s,content:i})=>({role:s,content:i})),stream:!0}),o=await this.makeRequest("/chat/completions",{method:"POST",body:l},!0);yield*this.processStream(o)}async makeRequest(t,n={},r=!1){var i;const l=qe(this.providerId),o=new Headers(n.headers||{});o.set("Content-Type","application/json"),o.set("Authorization",`Bearer ${l}`);const s=await fetch(`${this.baseUrl}${t}`,{...n,headers:o});if(!s.ok){const u=await s.json().catch(()=>({}));throw new Error(`API Error (${s.status}): ${((i=u.error)==null?void 0:i.message)||s.statusText}`)}return r?s:s.json()}async*processStream(t){var l,o,s;const n=(l=t.body)==null?void 0:l.getReader();if(!n)throw new Error("Failed to read stream.");const r=new TextDecoder;for(;;){const{done:i,value:u}=await n.read();if(i)break;const m=r.decode(u).split(`

`);for(const y of m)if(y.startsWith("data: ")){const h=y.substring(6);if(h.trim()==="[DONE]")return;try{const v=(s=(o=JSON.parse(h).choices[0])==null?void 0:o.delta)==null?void 0:s.content;v&&(yield v)}catch{Ke.error("Error parsing stream data chunk:",h)}}}}}class Am{constructor(){yr(this,"BASE_URL",ae.ENDPOINTS.ANTHROPIC)}async getModels(){return Promise.resolve([{id:"claude-3-opus-20240229",name:"Claude 3 Opus",contextLength:2e5},{id:"claude-3-sonnet-20240229",name:"Claude 3 Sonnet",contextLength:2e5},{id:"claude-3-haiku-20240307",name:"Claude 3 Haiku",contextLength:2e5},{id:"claude-2.1",name:"Claude 2.1",contextLength:2e5},{id:"claude-2.0",name:"Claude 2.0",contextLength:1e5}])}async*chat(t,n){var m,y,h;const r=qe("anthropic");if(!r)throw new Error("API key for Anthropic not found.");const l=(m=t.find(g=>g.role==="system"))==null?void 0:m.content,o=t.filter(g=>g.role!=="system"),s=JSON.stringify({model:n,messages:o.map(({role:g,content:v})=>({role:g,content:v})),...l&&{system:l},stream:!0,max_tokens:4096}),i=await fetch(`${this.BASE_URL}/messages`,{method:"POST",headers:{"Content-Type":"application/json","x-api-key":r,"anthropic-version":"2023-06-01"},body:s});if(!i.ok){const g=await i.json().catch(()=>({}));throw new Error(`API Error (${i.status}): ${((y=g.error)==null?void 0:y.message)||i.statusText}`)}const u=(h=i.body)==null?void 0:h.getReader();if(!u)throw new Error("Failed to read stream.");const a=new TextDecoder;for(;;){const{done:g,value:v}=await u.read();if(g)break;const k=a.decode(v).split(`
`);for(const d of k)if(d.startsWith("data: "))try{const c=JSON.parse(d.substring(6));c.type==="content_block_delta"&&c.delta.type==="text_delta"&&(yield c.delta.text)}catch{Ke.error("Error parsing Anthropic stream chunk:",d)}}}}class Fm{constructor(){yr(this,"BASE_URL",ae.ENDPOINTS.GOOGLE_GEMINI)}async getModels(){const t=qe("google");return t?(await(await fetch(`${this.BASE_URL}?key=${t}`)).json()).models.filter(l=>l.supportedGenerationMethods.includes("generateContent")).map(l=>({id:l.name,name:l.displayName,description:l.description,contextLength:l.inputTokenLimit})).sort((l,o)=>l.name.localeCompare(o.name)):[]}async*chat(t,n){var m,y,h,g,v,x,k;const r=qe("google");if(!r)throw new Error("API key for Google Gemini not found.");const l=t.map(d=>({role:d.role==="assistant"?"model":"user",parts:[{text:d.content}]})),o=JSON.stringify({contents:l}),s=n.split("/").pop(),i=await fetch(`${this.BASE_URL}/${s}:streamGenerateContent?key=${r}&alt=sse`,{method:"POST",headers:{"Content-Type":"application/json"},body:o});if(!i.ok){const d=await i.json().catch(()=>({}));throw new Error(`API Error (${i.status}): ${((m=d.error)==null?void 0:m.message)||i.statusText}`)}const u=(y=i.body)==null?void 0:y.getReader();if(!u)throw new Error("Failed to read stream.");const a=new TextDecoder;for(;;){const{done:d,value:c}=await u.read();if(d)break;const w=a.decode(c).split(`
`);for(const E of w)if(E.startsWith("data: "))try{const _=(k=(x=(v=(g=(h=JSON.parse(E.substring(6)).candidates)==null?void 0:h[0])==null?void 0:g.content)==null?void 0:v.parts)==null?void 0:x[0])==null?void 0:k.text;_&&(yield _)}catch{}}}}class Um extends Yr{constructor(){super("openrouter",ae.ENDPOINTS.OPENROUTER)}async makeRequest(t,n={},r=!1){var i;const l=qe(this.providerId),o=new Headers(n.headers||{});o.set("Content-Type","application/json"),o.set("Authorization",`Bearer ${l}`),o.set("HTTP-Referer",ae.OPENROUTER_REFERRER_URL),o.set("X-Title",ae.OPENROUTER_APP_TITLE);const s=await fetch(`${this.baseUrl}${t}`,{...n,headers:o});if(!s.ok){const u=await s.json().catch(()=>({}));throw new Error(`API Error (${s.status}): ${((i=u.error)==null?void 0:i.message)||s.statusText}`)}return r?s:s.json()}}class $m{constructor(t){this.baseUrl=t}async getModels(){try{const t=await fetch(`${this.baseUrl}/api/tags`);return t.ok?(await t.json()).models.map(r=>({id:r.name,name:r.name,description:`Size: ${(r.size/1e9).toFixed(2)} GB`})).sort((r,l)=>r.name.localeCompare(l.name)):[]}catch(t){return Ke.error("Failed to connect to Ollama:",t),[]}}async*chat(t,n){var i,u;const r=JSON.stringify({model:n,messages:t.map(({role:a,content:m})=>({role:a,content:m})),stream:!0}),l=await fetch(`${this.baseUrl}/api/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:r});if(!l.ok){const a=await l.json().catch(()=>({}));throw new Error(`Ollama Error (${l.status}): ${a.error||l.statusText}`)}const o=(i=l.body)==null?void 0:i.getReader();if(!o)throw new Error("Failed to read stream.");const s=new TextDecoder;for(;;){const{done:a,value:m}=await o.read();if(a)break;const h=s.decode(m).split(`
`);for(const g of h)if(g.trim())try{const v=JSON.parse(g);if((u=v.message)!=null&&u.content&&(yield v.message.content),v.done)return}catch{Ke.error("Error parsing Ollama stream chunk:",g)}}}}function pr(e,t){const n=We.getState().providers[e];switch(e){case"openai":return new Yr(e,ae.ENDPOINTS.OPENAI);case"groq":return new Yr(e,ae.ENDPOINTS.GROQ);case"deepseek":return new Yr(e,ae.ENDPOINTS.DEEPSEEK);case"anthropic":return new Am;case"google":return new Fm;case"openrouter":return new Um;case"ollama":const r=(t==null?void 0:t.baseURL)||(n==null?void 0:n.baseURL)||ae.DEFAULT_OLLAMA_URL;return new $m(r);default:const l=e;throw new Error(`Provider ${l} not implemented.`)}}const xu=()=>({id:Jc(),title:"New Chat",messages:[],createdAt:new Date().toISOString()}),gn=Dl()(Xc((e,t)=>({conversations:{},currentConversationId:null,isLoading:!1,addMessage:(n,r)=>{const l={...n,id:Jc(),timestamp:new Date().toISOString(),status:"sent"};return e(o=>{const s=o.conversations[r];return{conversations:{...o.conversations,[r]:{...s,messages:[...s.messages,l]}}}}),l.id},updateMessageStatus:(n,r)=>{e(l=>{const{currentConversationId:o,conversations:s}=l;if(!o||!s[o])return{};const i=s[o].messages,u=i.findIndex(m=>m.id===n);if(u===-1)return{};const a=[...i];return a[u]={...a[u],status:r},{conversations:{...l.conversations,[o]:{...s[o],messages:a}}}})},appendTokenToMessage:(n,r)=>{e(l=>{const{currentConversationId:o,conversations:s}=l;if(!o||!s[o])return{};const i=s[o].messages,u=i.findIndex(y=>y.id===n);if(u===-1)return{};const a=[...i],m=a[u];return a[u]={...m,content:m.content+r},{conversations:{...l.conversations,[o]:{...s[o],messages:a}}}})},sendChatMessage:async(n,r,l)=>{let{currentConversationId:o}=t();if(!o){const i=xu();e(u=>({conversations:{...u.conversations,[i.id]:i},currentConversationId:i.id})),o=i.id}t().addMessage({role:"user",content:n},o);const s=t().addMessage({role:"assistant",content:""},o);t().updateMessageStatus(s,"streaming"),e({isLoading:!0});try{const i=pr(r),u=t().conversations[o].messages.slice(0,-1),a=i.chat(u,l);for await(const m of a)t().appendTokenToMessage(s,m);t().updateMessageStatus(s,"sent")}catch(i){t().updateMessageStatus(s,"error"),t().appendTokenToMessage(s,`**Error:** ${i.message}`),Ct.getState().addToast({message:i.message,type:"error"})}finally{e({isLoading:!1})}},retryLastUserMessage:async()=>{const{useSettingsStore:n}=await nd(async()=>{const{useSettingsStore:y}=await Promise.resolve().then(()=>Om);return{useSettingsStore:y}},void 0,import.meta.url),{currentConversationId:r,conversations:l}=t(),{selectedProvider:o,selectedModel:s}=n.getState();if(!r||!l[r])return;const i=l[r].messages,u=i.map(y=>y.role).lastIndexOf("assistant");if(u===-1||i[u].status!=="error")return;const a=i[u-1];if((a==null?void 0:a.role)!=="user")return;const m=i.slice(0,u);e(y=>({conversations:{...y.conversations,[r]:{...l[r],messages:m}}})),t().sendChatMessage(a.content,o,s)},startNewConversation:()=>{const n=xu();e(r=>({conversations:{...r.conversations,[n.id]:n},currentConversationId:n.id}))},setCurrentConversationId:n=>e({currentConversationId:n}),clearAllConversations:()=>e({conversations:{},currentConversationId:null})}),{name:"sahai-chat-storage"})),Hm=()=>{const e=We(a=>a.selectedProvider),t=fr(a=>a.openModal),[n,r]=T.useState("idle"),[l,o]=T.useState(null),s=T.useCallback(async()=>{if(!e){r("idle");return}r("loading");const a=Date.now();try{await pr(e).getModels(),o(Date.now()-a),r("ok")}catch{r("error"),o(null)}},[e]);T.useEffect(()=>{s();const a=setInterval(s,ae.HEALTH_CHECK_INTERVAL_MS);return()=>clearInterval(a)},[s]);const i={idle:"bg-gray-400",loading:"bg-yellow-500 animate-pulse",ok:"bg-green-500",error:"bg-red-500"},u={idle:"Status: Idle",loading:"Status: Checking connection...",ok:`Status: Connected | Latency: ${l??"N/A"}ms. Click for details.`,error:"Status: Connection failed. Click for details."};return f.jsx("div",{className:"flex items-center gap-2 cursor-pointer",title:u[n],onClick:()=>t("status"),children:n==="loading"?f.jsx(dr,{size:14,className:"animate-spin text-yellow-500"}):f.jsx("div",{className:`w-3 h-3 rounded-full ${i[n]}`})})},Bm=({options:e,value:t,onChange:n,placeholder:r="Select a model...",disabled:l=!1})=>{const[o,s]=T.useState(!1),[i,u]=T.useState(""),[a,m]=T.useState(-1),y=T.useRef(null),h=T.useRef(null),g=T.useMemo(()=>e.find(d=>d.id===t),[e,t]),v=T.useMemo(()=>e.filter(d=>d.name.toLowerCase().includes(i.toLowerCase())||d.id.toLowerCase().includes(i.toLowerCase())),[e,i]);T.useEffect(()=>{const d=c=>{y.current&&!y.current.contains(c.target)&&s(!1)};return document.addEventListener("mousedown",d),()=>document.removeEventListener("mousedown",d)},[]),T.useEffect(()=>{const d=c=>{if(o)switch(c.key){case"ArrowDown":c.preventDefault(),m(p=>Math.min(p+1,v.length-1));break;case"ArrowUp":c.preventDefault(),m(p=>Math.max(p-1,0));break;case"Enter":c.preventDefault(),a>=0&&v[a]&&x(v[a]);break;case"Escape":s(!1);break}};return window.addEventListener("keydown",d),()=>window.removeEventListener("keydown",d)},[o,a,v]);const x=d=>{n(d.id),u(""),s(!1)},k=()=>{l||(s(!0),setTimeout(()=>{var d;return(d=h.current)==null?void 0:d.focus()},0))};return f.jsxs("div",{ref:y,className:"relative w-full",children:[f.jsxs("button",{onClick:k,disabled:l,className:"flex items-center justify-between w-full px-2 py-1 text-left bg-adobe-secondary rounded border border-transparent hover:border-adobe disabled:opacity-50",children:[f.jsx("span",{className:"truncate text-xs",children:(g==null?void 0:g.name)||r}),f.jsx(ri,{size:14,className:"text-gray-400"})]}),o&&f.jsxs("div",{className:"absolute z-10 w-full mt-1 bg-adobe-bg border border-adobe rounded-md shadow-lg max-h-60 overflow-y-auto",children:[f.jsx("div",{className:"p-2 border-b border-adobe sticky top-0 bg-adobe-bg",children:f.jsxs("div",{className:"relative",children:[f.jsx(Qp,{size:14,className:"absolute left-2 top-1/2 -translate-y-1/2 text-gray-400"}),f.jsx("input",{ref:h,type:"text",value:i,onChange:d=>u(d.target.value),placeholder:"Search models...",className:"w-full pl-7 pr-2 py-1 bg-adobe-secondary border border-adobe rounded text-xs focus:outline-none"})]})}),f.jsx("ul",{className:"py-1",children:v.length>0?v.map((d,c)=>f.jsx("li",{onClick:()=>x(d),onMouseEnter:()=>m(c),className:`px-3 py-1.5 text-xs cursor-pointer ${c===a?"bg-blue-600 text-white":"hover:bg-adobe-secondary"} ${t===d.id?"font-bold":""}`,children:d.name},d.id)):f.jsx("li",{className:"px-3 py-1.5 text-xs text-gray-500",children:"No results found"})})]})]})},wu=[{id:"openai",name:"OpenAI",icon:f.jsx(_e,{size:14})},{id:"anthropic",name:"Anthropic",icon:f.jsx(_e,{size:14})},{id:"google",name:"Google Gemini",icon:f.jsx(_e,{size:14})},{id:"groq",name:"Groq",icon:f.jsx(_e,{size:14})},{id:"deepseek",name:"DeepSeek",icon:f.jsx(_e,{size:14})},{id:"openrouter",name:"OpenRouter",icon:f.jsx(_e,{size:14})},{id:"ollama",name:"Ollama",icon:f.jsx($c,{size:14})}],Vm=({value:e,onChange:t})=>{const[n,r]=T.useState(!1),l=T.useRef(null),o=wu.find(i=>i.id===e);T.useEffect(()=>{const i=u=>{l.current&&!l.current.contains(u.target)&&r(!1)};return document.addEventListener("mousedown",i),()=>document.removeEventListener("mousedown",i)},[]);const s=i=>{t(i),r(!1)};return f.jsxs("div",{ref:l,className:"relative w-full",children:[f.jsxs("button",{onClick:()=>r(!n),className:"flex items-center justify-between w-full px-2 py-1 text-left bg-adobe-secondary rounded border border-transparent hover:border-adobe",children:[f.jsxs("div",{className:"flex items-center gap-2 min-w-0",children:[o==null?void 0:o.icon,f.jsx("span",{className:"truncate text-xs font-bold capitalize",children:(o==null?void 0:o.name)||e})]}),f.jsx(ri,{size:14,className:"text-gray-400 flex-shrink-0"})]}),n&&f.jsx("div",{className:"absolute z-10 w-full mt-1 bg-adobe-bg border border-adobe rounded-md shadow-lg max-h-60 overflow-y-auto",children:f.jsx("ul",{className:"py-1",children:wu.map(i=>f.jsx("li",{children:f.jsxs("button",{onClick:()=>s(i.id),className:`w-full flex items-center gap-2 px-3 py-1.5 text-xs text-left cursor-pointer ${e===i.id?"bg-blue-600 text-white font-bold":"hover:bg-adobe-secondary"}`,children:[i.icon,i.name]})},i.id))})})]})},Km=()=>{var a;const e=fr(m=>m.openModal),t=gn(m=>m.startNewConversation),{selectedProvider:n,selectedModel:r,setSelectedModel:l,setSelectedProvider:o,providers:s,refreshProviderModels:i}=We(),u=((a=s[n])==null?void 0:a.models)||[];return T.useEffect(()=>{i(n)},[n,i]),f.jsxs("header",{className:"flex items-center justify-between p-2 border-b border-adobe bg-adobe-secondary flex-shrink-0 gap-2",children:[f.jsxs("div",{className:"flex items-center gap-2 flex-shrink min-w-0",children:[f.jsx(Hm,{}),f.jsxs("div",{className:"flex items-center gap-2 min-w-0",children:[f.jsx("div",{className:"w-32 flex-shrink-0",children:f.jsx(Vm,{value:n,onChange:o})}),f.jsx("div",{className:"w-48 flex-shrink-0",children:f.jsx(Bm,{options:u,value:r,onChange:l,placeholder:u.length===0?"No models found":"Select a model",disabled:u.length===0})})]})]}),f.jsxs("div",{className:"flex items-center gap-1 flex-shrink-0",children:[f.jsx("button",{onClick:t,className:"p-1.5 rounded hover:bg-adobe-bg","aria-label":"New Chat",children:f.jsx(Wp,{size:16})}),f.jsx("button",{onClick:()=>e("history"),className:"p-1.5 rounded hover:bg-adobe-bg","aria-label":"Chat History",children:f.jsx(Hp,{size:16})}),f.jsx("button",{onClick:()=>e("settings"),className:"p-1.5 rounded hover:bg-adobe-bg","aria-label":"Settings",children:f.jsx(Yp,{size:16})})]})]})};let Ir=null;const Wm=["javascript","typescript","tsx","html","css","json","xml","markdown","yaml","scss","less","python","swift","rust","go","java","php","ruby","shell","shellscript"];async function Qm(){return Ir||(Ir=await rd({themes:["github-dark"],langs:Wm}),Ir)}async function Gm(e,t){const n=await Qm(),r=n.getLoadedLanguages().includes(t)?t:"txt";return n.codeToHtml(e,{lang:r,theme:"github-dark"})}const Ym=({code:e,language:t})=>{const[n,r]=T.useState(""),[l,o]=T.useState(!1),s=Ct(a=>a.addToast);T.useEffect(()=>{Gm(e,t).then(r)},[e,t]);const i=()=>{navigator.clipboard.writeText(e),s({message:"Code copied to clipboard",type:"success"})},u=async()=>{try{const a=await Im(`runCode(${JSON.stringify(e)})`);s({message:`Execution result: ${a.result||a.error}`,type:a.success?"info":"error"})}catch(a){s({message:`Execution failed: ${a.message}`,type:"error"})}};return f.jsxs("div",{className:"bg-[#282c34] rounded-md my-2 text-sm overflow-hidden",children:[f.jsxs("div",{className:"flex justify-between items-center px-3 py-1 bg-gray-700 text-gray-300",children:[f.jsx("span",{className:"font-mono",children:t}),f.jsxs("div",{className:"flex gap-1",children:[f.jsx("button",{onClick:()=>o(!l),className:"p-1 hover:bg-gray-600 rounded",children:l?f.jsx(ri,{size:14}):f.jsx(Ip,{size:14})}),f.jsx("button",{onClick:i,className:"p-1 hover:bg-gray-600 rounded",children:f.jsx(Ap,{size:14})}),f.jsx("button",{className:"p-1 hover:bg-gray-600 rounded",children:f.jsx(Fp,{size:14})}),["javascript","typescript","extendscript","jsx"].includes(t)&&f.jsx("button",{onClick:u,className:"p-1 hover:bg-gray-600 rounded",children:f.jsx(Xp,{size:14})})]})]}),!l&&f.jsx("div",{className:"p-3 overflow-x-auto",dangerouslySetInnerHTML:{__html:n}})]})},Xm=({message:e})=>{const t=e.role==="user",{retryLastUserMessage:n}=gn(),r=e.content.split(/(```[\w\s]*\n[\s\S]*?\n```)/g);return f.jsxs("div",{className:`flex items-start gap-3 ${t?"justify-end":"self-start"}`,children:[!t&&f.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center",children:f.jsx(Mp,{size:18})}),f.jsxs("div",{className:`max-w-[85%] rounded-lg p-3 ${t?"bg-blue-600 text-white":e.status==="error"?"bg-red-100 text-red-800":"bg-adobe-secondary"}`,children:[r.map((l,o)=>{if(l.startsWith("```")){const s=l.match(/```(\w*)\n([\s\S]*?)\n```/);if(s){const i=s[1]||"plaintext",u=s[2]||"";return f.jsx(Ym,{code:u,language:i},o)}}return f.jsx("p",{className:"whitespace-pre-wrap",dangerouslySetInnerHTML:{__html:l.replace(/\*\*(.*?)\*\*/g,"<b>$1</b>")}},o)}),e.status==="error"&&!t&&f.jsx("div",{className:"mt-2 pt-2 border-t border-red-300 flex justify-end",children:f.jsxs("button",{onClick:n,className:"flex items-center gap-2 text-xs font-semibold text-red-700 hover:text-red-900",children:[f.jsx(Fc,{size:12}),"Try again"]})})]}),t&&f.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center",children:f.jsx(Jp,{size:18})})]})},Jm=()=>f.jsxs("div",{className:"flex items-center gap-2 self-start",children:[f.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center",children:f.jsx(dr,{size:18,className:"animate-spin"})}),f.jsx("div",{className:"bg-adobe-secondary rounded-lg p-3",children:f.jsxs("div",{className:"flex items-center gap-1.5",children:[f.jsx("span",{className:"h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-75"}),f.jsx("span",{className:"h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-150"}),f.jsx("span",{className:"h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-300"})]})})]}),Zm=()=>{var o;const{conversations:e,currentConversationId:t,isLoading:n}=gn(),r=t?(o=e[t])==null?void 0:o.messages:[],l=T.useRef(null);return T.useEffect(()=>{l.current&&(l.current.scrollTop=l.current.scrollHeight)},[r]),f.jsx("div",{ref:l,className:"flex-grow p-4 overflow-y-auto",children:f.jsxs("div",{className:"flex flex-col gap-4",children:[r&&r.map(s=>f.jsx(Xm,{message:s},s.id)),n&&r&&r.length>0&&r[r.length-1].content===""&&f.jsx(Jm,{})]})})},qm=()=>{const[e,t]=T.useState(""),[n,r]=T.useState(!1),{sendChatMessage:l,isLoading:o}=gn(),{selectedProvider:s,selectedModel:i}=We(),u=Ct(v=>v.addToast),a=T.useRef(null),m=T.useRef(null),y=()=>{if(e.trim()&&!o){if(!i){u({message:"Please select a model first.",type:"warning"});return}l(e.trim(),s,i),t(""),setTimeout(()=>{var v;return(v=a.current)==null?void 0:v.focus()},0)}},h=v=>{v.key==="Enter"&&!v.shiftKey&&(v.preventDefault(),y())},g=()=>{var x;if(n){(x=m.current)==null||x.stop(),r(!1);return}const v=window.SpeechRecognition||window.webkitSpeechRecognition;if(!v){u({message:"Voice recognition is not supported in this browser.",type:"error"});return}m.current=new v,m.current.continuous=!0,m.current.interimResults=!0,m.current.lang="en-US",m.current.onstart=()=>r(!0),m.current.onend=()=>r(!1),m.current.onerror=k=>{u({message:`Voice recognition error: ${k.error}`,type:"error"}),r(!1)},m.current.onresult=k=>{let d="";for(let c=k.resultIndex;c<k.results.length;++c)k.results[c].isFinal?t(p=>p+k.results[c][0].transcript+" "):d+=k.results[c][0].transcript},m.current.start()};return T.useEffect(()=>{if(a.current){a.current.style.height="auto";const v=a.current.scrollHeight;a.current.style.height=`${v}px`}},[e]),f.jsx("div",{className:"p-2 border-t border-adobe bg-adobe-bg flex-shrink-0",children:f.jsxs("div",{className:"flex items-start gap-2 p-2 rounded-md bg-adobe-secondary",children:[f.jsx("button",{className:"p-1.5 rounded","aria-label":"Attach File",children:f.jsx(Kp,{size:18})}),f.jsx("button",{onClick:g,className:`p-1.5 rounded ${n?"bg-red-500 text-white":""}`,"aria-label":"Voice Input",children:f.jsx(Vp,{size:18})}),f.jsx("textarea",{ref:a,value:e,onChange:v=>t(v.target.value),onKeyDown:h,placeholder:n?"Listening...":"Type a message or use voice input...",className:"flex-grow bg-transparent focus:outline-none resize-none max-h-48 overflow-y-auto",rows:1,maxLength:4e3,disabled:o}),f.jsx("button",{onClick:y,disabled:o||!e.trim(),className:"p-1.5 rounded bg-blue-600 text-white disabled:bg-gray-500 self-end",children:o?f.jsx(dr,{size:18,className:"animate-spin"}):f.jsx(Gp,{size:18})})]})})},Al=({title:e,children:t})=>{const n=fr(r=>r.closeModal);return T.useEffect(()=>{const r=l=>{l.key==="Escape"&&n()};return window.addEventListener("keydown",r),()=>window.removeEventListener("keydown",r)},[n]),f.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex justify-center items-center z-50",onClick:n,children:f.jsxs("div",{className:"bg-adobe-bg rounded-lg shadow-xl w-full max-w-md m-4 flex flex-col",onClick:r=>r.stopPropagation(),children:[f.jsxs("header",{className:"flex justify-between items-center p-4 border-b border-adobe",children:[f.jsx("h2",{className:"text-lg font-semibold",children:e}),f.jsx("button",{onClick:n,className:"p-1 rounded-full hover:bg-adobe-secondary",children:f.jsx(Vc,{size:20})})]}),f.jsx("div",{className:"p-4 overflow-y-auto",children:t})]})})},bm=()=>{const{theme:e,setTheme:t}=We(),{clearAllConversations:n}=gn(),r=Ct(o=>o.addToast),l=()=>{window.confirm("Are you sure you want to delete all chat history? This action cannot be undone.")&&(n(),r({message:"All chat history has been cleared.",type:"success"}))};return f.jsx(Al,{title:"Settings",children:f.jsxs("div",{className:"flex flex-col gap-6",children:[f.jsxs("div",{children:[f.jsx("label",{htmlFor:"theme-select",className:"block text-sm font-medium mb-1",children:"Theme"}),f.jsxs("select",{id:"theme-select",value:e,onChange:o=>t(o.target.value),className:"w-full p-2 rounded bg-adobe-secondary border border-adobe focus:outline-none focus:ring-2 focus:ring-blue-500",children:[f.jsx("option",{value:"auto",children:"Auto (Sync with Adobe)"}),f.jsx("option",{value:"light",children:"Light"}),f.jsx("option",{value:"dark",children:"Dark"})]})]}),f.jsxs("div",{className:"border-t border-adobe pt-4",children:[f.jsx("h4",{className:"text-md font-semibold mb-2",children:"Data Management"}),f.jsxs("button",{onClick:l,className:"w-full flex items-center justify-center gap-2 px-3 py-2 rounded bg-red-600 hover:bg-red-700 text-white text-sm",children:[f.jsx(Hc,{size:16}),"Clear All Chat History"]}),f.jsx("p",{className:"text-xs text-gray-500 mt-2",children:"This will permanently delete all your conversations and messages stored within the extension."})]})]})})},Su=[{id:"openai",name:"OpenAI",icon:f.jsx(_e,{size:18})},{id:"anthropic",name:"Anthropic",icon:f.jsx(_e,{size:18})},{id:"google",name:"Google Gemini",icon:f.jsx(_e,{size:18})},{id:"groq",name:"Groq",icon:f.jsx(_e,{size:18})},{id:"deepseek",name:"DeepSeek",icon:f.jsx(_e,{size:18})},{id:"openrouter",name:"OpenRouter",icon:f.jsx(_e,{size:18})},{id:"ollama",name:"Ollama",icon:f.jsx($c,{size:18})}],eh=({providerId:e,providerName:t})=>{const[n,r]=T.useState(""),[l,o]=T.useState(!1),[s,i]=T.useState(!1),u=Ct(v=>v.addToast),{selectedProvider:a,setSelectedProvider:m}=We();T.useEffect(()=>{const v=qe(e);o(!!v),r("")},[e]);const y=()=>{if(!n.trim()){u({message:"API key cannot be empty.",type:"warning"});return}us(e,n.trim()),o(!0),r(""),i(!1),u({message:`${t} API key saved successfully.`,type:"success"})},h=()=>{us(e,""),o(!1),u({message:`${t} API key cleared.`,type:"info"})},g=()=>{m(e),u({message:`Switched to ${t} provider.`,type:"success"})};return f.jsxs("div",{className:"flex flex-col gap-4",children:[f.jsxs("h3",{className:"text-lg font-semibold",children:[t," Configuration"]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium mb-1",children:"API Key"}),f.jsxs("div",{className:"flex items-center gap-2",children:[f.jsx("input",{type:s?"text":"password",value:n,onChange:v=>r(v.target.value),placeholder:l?"•••••••••••••••• (Key is set)":"Enter your API key",className:"w-full p-2 rounded bg-adobe-secondary border border-adobe focus:outline-none focus:ring-2 focus:ring-blue-500"}),f.jsx("button",{onClick:()=>i(!s),className:"p-2 hover:bg-adobe-secondary rounded",children:s?f.jsx(Up,{size:18}):f.jsx($p,{size:18})})]}),f.jsxs("p",{className:`text-xs mt-1 ${l?"text-green-500":"text-yellow-500"}`,children:["Status: ",l?"API Key is configured":"API Key not set"]})]}),f.jsxs("div",{className:"flex justify-between",children:[f.jsx("button",{onClick:g,disabled:!l,className:"flex items-center gap-2 px-3 py-2 rounded bg-green-600 hover:bg-green-700 text-white text-sm disabled:bg-gray-500",children:a===e?"✓ Active Provider":"Use This Provider"}),f.jsxs("div",{className:"flex gap-2",children:[l&&f.jsxs("button",{onClick:h,className:"flex items-center gap-2 px-3 py-2 rounded bg-red-600 hover:bg-red-700 text-white text-sm",children:[f.jsx(Hc,{size:16}),"Clear Key"]}),f.jsxs("button",{onClick:y,disabled:!n.trim(),className:"flex items-center gap-2 px-3 py-2 rounded bg-blue-600 hover:bg-blue-700 text-white text-sm disabled:bg-gray-500",children:[f.jsx(Uc,{size:16}),"Save Key"]})]})]})]})},th=()=>{const{providers:e,setOllamaBaseUrl:t,selectedProvider:n,setSelectedProvider:r}=We(),[l,o]=T.useState(e.ollama.baseURL||"http://localhost:11434"),[s,i]=T.useState("idle"),u=Ct(g=>g.addToast),a=()=>{t(l),u({message:"Ollama Base URL updated.",type:"success"})},m=()=>{r("ollama"),u({message:"Switched to Ollama provider.",type:"success"})},y=async()=>{i("testing");try{const v=await pr("ollama",{baseURL:l}).getModels();v.length>0?(i("success"),u({message:`Connection successful! Found ${v.length} models.`,type:"success"})):(i("failed"),u({message:"Connection successful, but no models found.",type:"warning"}))}catch{i("failed"),u({message:"Failed to connect to Ollama. Check the URL and ensure Ollama is running.",type:"error"})}},h={idle:null,testing:f.jsx(dr,{size:18,className:"animate-spin text-yellow-500"}),success:f.jsx(li,{size:18,className:"text-green-500"}),failed:f.jsx(Ac,{size:18,className:"text-red-500"})}[s];return f.jsxs("div",{className:"flex flex-col gap-4",children:[f.jsx("h3",{className:"text-lg font-semibold",children:"Ollama (Local) Configuration"}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium mb-1",children:"Ollama Server URL"}),f.jsxs("div",{className:"flex items-center gap-2",children:[f.jsx("input",{type:"text",value:l,onChange:g=>o(g.target.value),placeholder:"e.g., http://localhost:11434",className:"w-full p-2 rounded bg-adobe-secondary border border-adobe focus:outline-none focus:ring-2 focus:ring-blue-500"}),h]})]}),f.jsxs("div",{className:"flex justify-between",children:[f.jsx("button",{onClick:m,className:"flex items-center gap-2 px-3 py-2 rounded bg-green-600 hover:bg-green-700 text-white text-sm",children:n==="ollama"?"✓ Active Provider":"Use This Provider"}),f.jsxs("div",{className:"flex gap-2",children:[f.jsx("button",{onClick:y,disabled:s==="testing",className:"flex items-center gap-2 px-3 py-2 rounded bg-gray-600 hover:bg-gray-700 text-white text-sm disabled:opacity-50",children:"Test Connection"}),f.jsxs("button",{onClick:a,className:"flex items-center gap-2 px-3 py-2 rounded bg-blue-600 hover:bg-blue-700 text-white text-sm",children:[f.jsx(Uc,{size:16}),"Save URL"]})]})]})]})},nh=()=>{const[e,t]=T.useState("openai"),n=()=>{const r=Su.find(l=>l.id===e);return r?r.id==="ollama"?f.jsx(th,{}):f.jsx(eh,{providerId:r.id,providerName:r.name}):null};return f.jsx(Al,{title:"Provider Configuration",children:f.jsxs("div",{className:"flex min-h-[300px]",children:[f.jsx("nav",{className:"w-1/3 border-r border-adobe pr-4",children:f.jsx("ul",{className:"flex flex-col gap-1",children:Su.map(r=>f.jsx("li",{children:f.jsxs("button",{onClick:()=>t(r.id),className:`w-full flex items-center gap-3 text-left p-2 rounded text-sm ${e===r.id?"bg-blue-600 text-white":"hover:bg-adobe-secondary"}`,children:[r.icon,r.name]})},r.id))})}),f.jsx("section",{className:"w-2/3 pl-4",children:n()})]})})},rh=()=>{const{conversations:e,setCurrentConversationId:t}=gn(),{closeModal:n}=fr(),r=Object.values(e),l=o=>{t(o),n()};return f.jsx(Al,{title:"Chat History",children:r.length===0?f.jsx("p",{children:"No chat history yet."}):f.jsx("ul",{className:"flex flex-col gap-2",children:r.map(o=>f.jsxs("li",{onClick:()=>l(o.id),className:"p-2 rounded hover:bg-adobe-secondary cursor-pointer",children:[f.jsx("p",{className:"font-semibold",children:o.title}),f.jsx("p",{className:"text-xs opacity-70",children:new Date(o.createdAt).toLocaleString()})]},o.id))})})},lh=["openai","anthropic","google","groq","deepseek","openrouter","ollama"],oh=({providerId:e})=>{const[t,n]=T.useState({status:"loading"}),r=async()=>{n({status:"loading"});const o=Date.now();try{const i=await pr(e).getModels();i.length>0?n({status:"success",latency:Date.now()-o,modelsCount:i.length}):n({status:"error",error:"Connected, but no models found."})}catch(s){const i=/API key/i.test(s.message);n({status:i?"unconfigured":"error",error:i?"API key not set or invalid.":"Connection failed."})}};T.useEffect(()=>{r()},[e]);const l={loading:f.jsx(dr,{size:16,className:"animate-spin text-yellow-500"}),success:f.jsx(li,{size:16,className:"text-green-500"}),error:f.jsx(Ac,{size:16,className:"text-red-500"}),unconfigured:f.jsx(Bc,{size:16,className:"text-orange-500"})};return f.jsxs("tr",{className:"border-b border-adobe",children:[f.jsx("td",{className:"p-2 font-semibold capitalize",children:e}),f.jsx("td",{className:"p-2 text-center",children:l[t.status]}),f.jsx("td",{className:"p-2 text-xs",children:t.latency?`${t.latency} ms`:"N/A"}),f.jsx("td",{className:"p-2 text-xs",children:t.modelsCount??"N/A"}),f.jsx("td",{className:"p-2 text-xs text-gray-400",children:t.error??"OK"}),f.jsx("td",{className:"p-2 text-center",children:f.jsx("button",{onClick:r,title:"Re-check status",children:f.jsx(Fc,{size:14,className:"hover:text-blue-500"})})})]})},sh=()=>f.jsx(Al,{title:"System Connection Status",children:f.jsxs("div",{className:"max-h-[60vh] overflow-y-auto",children:[f.jsxs("table",{className:"w-full text-sm text-left",children:[f.jsx("thead",{className:"bg-adobe-secondary sticky top-0",children:f.jsxs("tr",{className:"border-b-2 border-adobe",children:[f.jsx("th",{className:"p-2",children:"Provider"}),f.jsx("th",{className:"p-2 text-center",children:"Status"}),f.jsx("th",{className:"p-2",children:"Latency"}),f.jsx("th",{className:"p-2",children:"Models"}),f.jsx("th",{className:"p-2",children:"Details"}),f.jsx("th",{className:"p-2 text-center",children:"Check"})]})}),f.jsx("tbody",{children:lh.map(e=>f.jsx(oh,{providerId:e},e))})]}),f.jsx("p",{className:"text-xs text-gray-500 mt-4",children:"This panel shows the real-time status of each AI provider. Latency is measured by fetching the model list."})]})}),ih={success:f.jsx(li,{className:"text-green-500"}),error:f.jsx(Dp,{className:"text-red-500"}),warning:f.jsx(Bc,{className:"text-yellow-500"}),info:f.jsx(Bp,{className:"text-blue-500"})},uh={success:"bg-green-100 border-green-400",error:"bg-red-100 border-red-400",warning:"bg-yellow-100 border-yellow-400",info:"bg-blue-100 border-blue-400"},ah=()=>{const{toasts:e,removeToast:t}=Ct();return e.length?f.jsx("div",{className:"fixed top-4 right-4 z-50 flex flex-col gap-2",children:e.map(n=>f.jsxs("div",{className:`flex items-center gap-3 p-3 rounded-md shadow-lg border ${uh[n.type]}`,children:[ih[n.type],f.jsx("p",{className:"text-sm text-gray-800",children:n.message}),f.jsx("button",{onClick:()=>t(n.id),children:f.jsx(Vc,{size:16,className:"text-gray-500"})})]},n.id))}):null},ch=()=>{const{theme:e,applyTheme:t}=We(),n=fr(l=>l.activeModal);T.useEffect(()=>{t()},[e,t]);const r=()=>{switch(n){case"settings":return f.jsx(bm,{});case"provider":return f.jsx(nh,{});case"history":return f.jsx(rh,{});case"status":return f.jsx(sh,{});default:return null}};return f.jsxs("div",{className:"flex flex-col h-screen bg-adobe-bg text-adobe-text font-sans antialiased",children:[f.jsx(Km,{}),f.jsxs("main",{className:"flex-grow flex flex-col overflow-hidden",children:[f.jsx(Zm,{}),f.jsx(qm,{})]}),r(),f.jsx(ah,{})]})};class dh extends T.Component{constructor(){super(...arguments);yr(this,"state",{hasError:!1})}static getDerivedStateFromError(n){return{hasError:!0}}componentDidCatch(n,r){Ke.error("Uncaught error:",n,r),this.setState({error:n})}render(){var n;return this.state.hasError?f.jsxs("div",{className:"p-4 text-red-500 bg-red-100 border border-red-500 rounded-md",children:[f.jsx("h1",{className:"font-bold",children:"Something went wrong."}),f.jsx("p",{children:"An unexpected error occurred. Please try reloading the panel."}),f.jsx("details",{className:"mt-2 text-sm text-gray-700",children:(n=this.state.error)==null?void 0:n.toString()})]}):this.props.children}}Mm();ho.createRoot(document.getElementById("root")).render(f.jsx(Ou.StrictMode,{children:f.jsx(dh,{children:f.jsx(ch,{})})}));
