{"version": 3, "file": "grpc-client-base.js", "sourceRoot": "", "sources": ["grpc-client-base.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAA;AACxC,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,MAAM,CAAA;AAgCnC;;GAEG;AACH,SAAS,aAAa,CAAC,OAAY;IAClC,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QAC/C,OAAO,EAAE,CAAA;IACV,CAAC;SAAM,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;QACjD,OAAO,OAAO,CAAC,MAAM,EAAE,CAAA;IACxB,CAAC;SAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QACxC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAA;IACtB,CAAC;SAAM,CAAC;QACP,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAA;IAC1B,CAAC;AACF,CAAC;AAED,+DAA+D;AAC/D,MAAM,UAAU,gBAAgB,CAAyB,OAAU;IAClE,MAAM,MAAM,GAAG,EAAuB,CAAA;IAEtC,iCAAiC;IACjC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;QACjD,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YAC3B,kCAAkC;YAClC,MAAM,CAAC,MAAM,CAAC,IAA+B,CAAC,GAAG,CAAC,CACjD,OAAY,EACZ,OAIC,EACA,EAAE;gBACH,MAAM,SAAS,GAAG,MAAM,EAAE,CAAA;gBAE1B,0CAA0C;gBAC1C,MAAM,cAAc,GAAG,CAAC,KAAmB,EAAE,EAAE;oBAC9C,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAA;oBAC1B,IAAI,OAAO,CAAC,IAAI,KAAK,eAAe,IAAI,OAAO,CAAC,aAAa,EAAE,UAAU,KAAK,SAAS,EAAE,CAAC;wBACzF,IAAI,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;4BACjC,eAAe;4BACf,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gCACrB,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAA;4BACxD,CAAC;4BACD,0CAA0C;4BAC1C,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAA;wBACtD,CAAC;6BAAM,IAAI,OAAO,CAAC,aAAa,CAAC,YAAY,KAAK,KAAK,EAAE,CAAC;4BACzD,gBAAgB;4BAChB,IAAI,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;gCACnC,mCAAmC;gCACnC,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAA;gCACxC,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;gCACrE,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;4BAC7B,CAAC;4BAED,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gCACxB,OAAO,CAAC,UAAU,EAAE,CAAA;4BACrB,CAAC;4BACD,qEAAqE;4BACrE,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAA;wBACtD,CAAC;6BAAM,CAAC;4BACP,4BAA4B;4BAC5B,IAAI,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;gCACnC,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAA;gCACxC,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;gCACrE,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;4BAC7B,CAAC;wBACF,CAAC;oBACF,CAAC;gBACF,CAAC,CAAA;gBAED,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAA;gBAElD,6BAA6B;gBAC7B,MAAM,cAAc,GAAG,aAAa,CAAC,OAAO,CAAC,CAAA;gBAE7C,MAAM,CAAC,WAAW,CAAC;oBAClB,IAAI,EAAE,cAAc;oBACpB,YAAY,EAAE;wBACb,OAAO,EAAE,OAAO,CAAC,QAAQ;wBACzB,MAAM,EAAE,MAAM,CAAC,IAAI;wBACnB,OAAO,EAAE,cAAc;wBACvB,UAAU,EAAE,SAAS;wBACrB,YAAY,EAAE,IAAI;qBAClB;iBACD,CAAC,CAAA;gBAEF,yCAAyC;gBACzC,OAAO,GAAG,EAAE;oBACX,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAA;oBACrD,4BAA4B;oBAC5B,MAAM,CAAC,WAAW,CAAC;wBAClB,IAAI,EAAE,qBAAqB;wBAC3B,mBAAmB,EAAE;4BACpB,UAAU,EAAE,SAAS;yBACrB;qBACD,CAAC,CAAA;oBACF,OAAO,CAAC,GAAG,CAAC,0CAA0C,SAAS,EAAE,CAAC,CAAA;gBACnE,CAAC,CAAA;YACF,CAAC,CAAQ,CAAA;QACV,CAAC;aAAM,CAAC;YACP,8BAA8B;YAC9B,MAAM,CAAC,MAAM,CAAC,IAA+B,CAAC,GAAG,CAAC,CAAC,OAAY,EAAE,EAAE;gBAClE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACtC,MAAM,SAAS,GAAG,MAAM,EAAE,CAAA;oBAE1B,qDAAqD;oBACrD,MAAM,cAAc,GAAG,CAAC,KAAmB,EAAE,EAAE;wBAC9C,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAA;wBAC1B,IAAI,OAAO,CAAC,IAAI,KAAK,eAAe,IAAI,OAAO,CAAC,aAAa,EAAE,UAAU,KAAK,SAAS,EAAE,CAAC;4BACzF,2CAA2C;4BAC3C,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAA;4BAErD,IAAI,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;gCACjC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAA;4BAC/C,CAAC;iCAAM,CAAC;gCACP,wCAAwC;gCACxC,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAA;gCACxC,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;gCACrE,OAAO,CAAC,QAAQ,CAAC,CAAA;4BAClB,CAAC;wBACF,CAAC;oBACF,CAAC,CAAA;oBAED,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAA;oBAElD,mBAAmB;oBACnB,MAAM,cAAc,GAAG,aAAa,CAAC,OAAO,CAAC,CAAA;oBAE7C,MAAM,CAAC,WAAW,CAAC;wBAClB,IAAI,EAAE,cAAc;wBACpB,YAAY,EAAE;4BACb,OAAO,EAAE,OAAO,CAAC,QAAQ;4BACzB,MAAM,EAAE,MAAM,CAAC,IAAI;4BACnB,OAAO,EAAE,cAAc;4BACvB,UAAU,EAAE,SAAS;4BACrB,YAAY,EAAE,KAAK;yBACnB;qBACD,CAAC,CAAA;gBACH,CAAC,CAAC,CAAA;YACH,CAAC,CAAQ,CAAA;QACV,CAAC;IACF,CAAC,CAAC,CAAA;IAEF,OAAO,MAAM,CAAA;AACd,CAAC"}