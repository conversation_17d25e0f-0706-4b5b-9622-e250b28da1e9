/**
 * Integration test for provider selection and model loading functionality
 * 
 * This test verifies that:
 * 1. Provider selection works correctly
 * 2. Model loading is triggered when switching providers
 * 3. The UI updates appropriately when providers change
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useSettingsStore } from '../stores/settingsStore';
import { getProviderApi } from '../services/providerFactory';
import { ProviderID } from '../types';

// Mock the provider factory
vi.mock('../services/providerFactory');
vi.mock('../utils/cep');
vi.mock('../utils/logger');

const mockGetProviderApi = vi.mocked(getProviderApi);

describe('Provider Selection Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset the store to initial state
    useSettingsStore.setState({
      providers: {
        openai: { models: [] },
        anthropic: { models: [] },
        google: { models: [] },
        groq: { models: [] },
        deepseek: { models: [] },
        openrouter: { models: [] },
        ollama: { baseURL: 'http://localhost:11434', models: [] },
      },
      selectedProvider: 'openai',
      selectedModel: '',
      theme: 'auto',
      adobeTheme: null,
      modelCache: new Map(),
    });
  });

  it('should change selected provider and clear selected model', () => {
    const store = useSettingsStore.getState();
    
    // Set initial state
    store.setSelectedModel('gpt-4');
    expect(useSettingsStore.getState().selectedProvider).toBe('openai');
    expect(useSettingsStore.getState().selectedModel).toBe('gpt-4');
    
    // Change provider
    store.setSelectedProvider('anthropic');
    
    // Verify provider changed and model was cleared
    expect(useSettingsStore.getState().selectedProvider).toBe('anthropic');
    expect(useSettingsStore.getState().selectedModel).toBe('');
  });

  it('should trigger model refresh when switching to provider with no models', async () => {
    const mockProvider = {
      getModels: vi.fn().mockResolvedValue([
        { id: 'claude-3', name: 'Claude 3', description: 'Anthropic model' },
        { id: 'claude-2', name: 'Claude 2', description: 'Anthropic model' },
      ]),
      chat: vi.fn(),
    };
    
    mockGetProviderApi.mockReturnValue(mockProvider);
    
    const store = useSettingsStore.getState();
    
    // Switch to anthropic (which has no models initially)
    await store.setSelectedProvider('anthropic');
    
    // Wait for async model refresh
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Verify models were fetched and stored
    const state = useSettingsStore.getState();
    expect(state.providers.anthropic.models).toHaveLength(2);
    expect(state.providers.anthropic.models[0].id).toBe('claude-3');
  });

  it('should auto-select first model when models are loaded', async () => {
    const mockProvider = {
      getModels: vi.fn().mockResolvedValue([
        { id: 'model-1', name: 'Model 1', description: 'First model' },
        { id: 'model-2', name: 'Model 2', description: 'Second model' },
      ]),
      chat: vi.fn(),
    };
    
    mockGetProviderApi.mockReturnValue(mockProvider);
    
    const store = useSettingsStore.getState();
    
    // Refresh models for a provider
    await store.refreshProviderModels('groq');
    
    // Wait for async operation
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Verify first model was auto-selected
    const state = useSettingsStore.getState();
    expect(state.selectedModel).toBe('model-1');
  });

  it('should handle provider API errors gracefully', async () => {
    const mockProvider = {
      getModels: vi.fn().mockRejectedValue(new Error('API Error')),
      chat: vi.fn(),
    };
    
    mockGetProviderApi.mockReturnValue(mockProvider);
    
    const store = useSettingsStore.getState();
    
    // Try to refresh models for a provider that will fail
    await store.refreshProviderModels('openai');
    
    // Wait for async operation
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Verify models array is empty on error
    const state = useSettingsStore.getState();
    expect(state.providers.openai.models).toHaveLength(0);
  });

  it('should use cached models when available and fresh', () => {
    const store = useSettingsStore.getState();
    const mockModels = [
      { id: 'cached-model', name: 'Cached Model', description: 'From cache' },
    ];
    
    // Set up cache with fresh data
    const cache = new Map();
    cache.set('openai', { models: mockModels, timestamp: Date.now() });
    
    useSettingsStore.setState({ modelCache: cache });
    
    // Refresh models - should use cache
    store.refreshProviderModels('openai');
    
    // Verify cached models were used
    const state = useSettingsStore.getState();
    expect(state.providers.openai.models).toEqual(mockModels);
  });

  it('should validate all provider IDs are handled', () => {
    const providerIds: ProviderID[] = ['openai', 'anthropic', 'google', 'groq', 'deepseek', 'openrouter', 'ollama'];
    
    providerIds.forEach(providerId => {
      expect(() => {
        const store = useSettingsStore.getState();
        store.setSelectedProvider(providerId);
      }).not.toThrow();
      
      expect(useSettingsStore.getState().selectedProvider).toBe(providerId);
    });
  });
});
