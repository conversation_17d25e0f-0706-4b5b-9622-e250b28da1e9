import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, KeyRound, Server } from 'lucide-react';
import { ProviderID } from '../../types';

interface ProviderSelectProps {
  value: ProviderID;
  onChange: (providerId: ProviderID) => void;
}

// Provider definitions with icons and display names
const PROVIDERS: { id: ProviderID; name: string; icon: React.ReactNode }[] = [
  { id: 'openai', name: 'OpenAI', icon: <KeyRound size={14} /> },
  { id: 'anthropic', name: 'Anthropic', icon: <KeyRound size={14} /> },
  { id: 'google', name: 'Google Gemini', icon: <KeyRound size={14} /> },
  { id: 'groq', name: 'Groq', icon: <KeyRound size={14} /> },
  { id: 'deepseek', name: 'DeepSeek', icon: <KeyRound size={14} /> },
  { id: 'openrouter', name: 'OpenRouter', icon: <KeyRound size={14} /> },
  { id: 'ollama', name: 'Ollama', icon: <Server size={14} /> },
];

const ProviderSelect: React.FC<ProviderSelectProps> = ({ value, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const selectedProvider = PROVIDERS.find(p => p.id === value);

  // Close dropdown on click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelect = (providerId: ProviderID) => {
    onChange(providerId);
    setIsOpen(false);
  };

  return (
    <div ref={containerRef} className="relative w-full">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-2 py-1 text-left bg-adobe-secondary rounded border border-transparent hover:border-adobe"
      >
        <div className="flex items-center gap-2 min-w-0">
          {selectedProvider?.icon}
          <span className="truncate text-xs font-bold capitalize">
            {selectedProvider?.name || value}
          </span>
        </div>
        <ChevronDown size={14} className="text-gray-400 flex-shrink-0" />
      </button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-adobe-bg border border-adobe rounded-md shadow-lg max-h-60 overflow-y-auto">
          <ul className="py-1">
            {PROVIDERS.map((provider) => (
              <li key={provider.id}>
                <button
                  onClick={() => handleSelect(provider.id)}
                  className={`w-full flex items-center gap-2 px-3 py-1.5 text-xs text-left cursor-pointer ${
                    value === provider.id 
                      ? 'bg-blue-600 text-white font-bold' 
                      : 'hover:bg-adobe-secondary'
                  }`}
                >
                  {provider.icon}
                  {provider.name}
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default ProviderSelect;
