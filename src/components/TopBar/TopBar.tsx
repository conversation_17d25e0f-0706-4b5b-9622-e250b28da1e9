import React from 'react';
import { Plus, History, Settings } from 'lucide-react';
import { useModalStore } from '../../stores/modalStore';
import { useChatStore } from '../../stores/chatStore';
import StatusIndicator from './StatusIndicator';
import { useSettingsStore } from '../../stores/settingsStore';
import SearchableSelect from '../Common/SearchableSelect';
import ProviderSelect from './ProviderSelect';

const TopBar: React.FC = () => {
  const openModal = useModalStore((s) => s.openModal);
  const startNewConversation = useChatStore(s => s.startNewConversation);
  const {
      selectedProvider,
      selectedModel,
      setSelectedModel,
      setSelectedProvider,
      providers
  } = useSettingsStore();

  const modelsForProvider = providers[selectedProvider]?.models || [];

  return (
    <header className="flex items-center justify-between p-2 border-b border-adobe bg-adobe-secondary flex-shrink-0 gap-2">
      <div className="flex items-center gap-2 flex-shrink min-w-0">
        <StatusIndicator />
        <div className="flex flex-col gap-1 min-w-0">
            <ProviderSelect
                value={selectedProvider}
                onChange={setSelectedProvider}
            />
            <SearchableSelect
                options={modelsForProvider}
                value={selectedModel}
                onChange={setSelectedModel}
                placeholder={modelsForProvider.length === 0 ? "No models found" : "Select a model"}
                disabled={modelsForProvider.length === 0}
            />
        </div>
      </div>
      <div className="flex items-center gap-1 flex-shrink-0">
        <button onClick={startNewConversation} className="p-1.5 rounded hover:bg-adobe-bg" aria-label="New Chat"><Plus size={16} /></button>
        <button onClick={() => openModal('history')} className="p-1.5 rounded hover:bg-adobe-bg" aria-label="Chat History"><History size={16} /></button>
        <button onClick={() => openModal('settings')} className="p-1.5 rounded hover:bg-adobe-bg" aria-label="Settings"><Settings size={16} /></button>
      </div>
    </header>
  );
};

export default TopBar;
