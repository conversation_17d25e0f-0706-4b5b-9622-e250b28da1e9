import React, { useState, useEffect } from 'react';
import { KeyRound, Server, Eye, EyeOff, Save, Trash2, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import Modal from '../Common/Modal';
import { useSettingsStore } from '../../stores/settingsStore';
import { useToastStore } from '../../stores/toastStore';
import { ProviderID } from '../../types';
import { getSecureCredential, setSecureCredential } from '../../utils/cep';
import { getProviderApi } from '../../services/providerFactory';

// List of all providers to be configured
const PROVIDERS: { id: ProviderID; name: string; icon: React.ReactNode }[] = [
  { id: 'openai', name: 'OpenAI', icon: <KeyRound size={18} /> },
  { id: 'anthropic', name: 'Anthropic', icon: <KeyRound size={18} /> },
  { id: 'google', name: 'Google Gemini', icon: <KeyRound size={18} /> },
  { id: 'groq', name: 'Groq', icon: <KeyRound size={18} /> },
  { id: 'deepseek', name: 'DeepSeek', icon: <KeyRound size={18} /> },
  { id: 'openrouter', name: 'OpenRouter', icon: <KeyRound size={18} /> },
  { id: 'ollama', name: 'Ollama', icon: <Server size={18} /> },
];

/**
 * A reusable component for providers that use an API key.
 */
const ApiKeySettings: React.FC<{ providerId: ProviderID; providerName: string }> = ({ providerId, providerName }) => {
  const [apiKey, setApiKey] = useState('');
  const [isKeySet, setIsKeySet] = useState(false);
  const [showKey, setShowKey] = useState(false);
  const addToast = useToastStore((s) => s.addToast);
  const { selectedProvider, setSelectedProvider } = useSettingsStore();

  useEffect(() => {
    // Check if a key is already stored when the component mounts or provider changes
    const storedKey = getSecureCredential(providerId);
    setIsKeySet(!!storedKey);
    setApiKey(''); // Clear input on change
  }, [providerId]);

  const handleSaveKey = () => {
    if (!apiKey.trim()) {
      addToast({ message: 'API key cannot be empty.', type: 'warning' });
      return;
    }
    setSecureCredential(providerId, apiKey.trim());
    setIsKeySet(true);
    setApiKey('');
    setShowKey(false);
    addToast({ message: `${providerName} API key saved successfully.`, type: 'success' });
  };

  const handleClearKey = () => {
    setSecureCredential(providerId, '');
    setIsKeySet(false);
    addToast({ message: `${providerName} API key cleared.`, type: 'info' });
  };

  const handleUseProvider = () => {
    setSelectedProvider(providerId);
    addToast({ message: `Switched to ${providerName} provider.`, type: 'success' });
  };

  return (
    <div className="flex flex-col gap-4">
      <h3 className="text-lg font-semibold">{providerName} Configuration</h3>
      <div>
        <label className="block text-sm font-medium mb-1">API Key</label>
        <div className="flex items-center gap-2">
          <input
            type={showKey ? 'text' : 'password'}
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            placeholder={isKeySet ? '•••••••••••••••• (Key is set)' : 'Enter your API key'}
            className="w-full p-2 rounded bg-adobe-secondary border border-adobe focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button onClick={() => setShowKey(!showKey)} className="p-2 hover:bg-adobe-secondary rounded">
            {showKey ? <EyeOff size={18} /> : <Eye size={18} />}
          </button>
        </div>
        <p className={`text-xs mt-1 ${isKeySet ? 'text-green-500' : 'text-yellow-500'}`}>
          Status: {isKeySet ? 'API Key is configured' : 'API Key not set'}
        </p>
      </div>
      <div className="flex justify-between">
        <button
          onClick={handleUseProvider}
          disabled={!isKeySet}
          className="flex items-center gap-2 px-3 py-2 rounded bg-green-600 hover:bg-green-700 text-white text-sm disabled:bg-gray-500"
        >
          {selectedProvider === providerId ? '✓ Active Provider' : 'Use This Provider'}
        </button>
        <div className="flex gap-2">
          {isKeySet && (
            <button
              onClick={handleClearKey}
              className="flex items-center gap-2 px-3 py-2 rounded bg-red-600 hover:bg-red-700 text-white text-sm"
            >
              <Trash2 size={16} />
              Clear Key
            </button>
          )}
          <button
            onClick={handleSaveKey}
            disabled={!apiKey.trim()}
            className="flex items-center gap-2 px-3 py-2 rounded bg-blue-600 hover:bg-blue-700 text-white text-sm disabled:bg-gray-500"
          >
            <Save size={16} />
            Save Key
          </button>
        </div>
      </div>
    </div>
  );
};


/**
 * A dedicated component for configuring the local Ollama provider.
 */
const OllamaSettings: React.FC = () => {
  const { providers, setOllamaBaseUrl, selectedProvider, setSelectedProvider } = useSettingsStore();
  const [baseUrl, setBaseUrl] = useState(providers.ollama.baseURL || 'http://localhost:11434');
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'testing' | 'success' | 'failed'>('idle');
  const addToast = useToastStore((s) => s.addToast);

  const handleSaveUrl = () => {
    setOllamaBaseUrl(baseUrl);
    addToast({ message: 'Ollama Base URL updated.', type: 'success' });
  };

  const handleUseProvider = () => {
    setSelectedProvider('ollama');
    addToast({ message: 'Switched to Ollama provider.', type: 'success' });
  };
  
  const handleTestConnection = async () => {
    setConnectionStatus('testing');
    try {
      // Cleanly pass the override config to the factory
      const testProvider = getProviderApi('ollama', { baseURL: baseUrl });
      const models = await testProvider.getModels();

      if (models.length > 0) {
        setConnectionStatus('success');
        addToast({ message: `Connection successful! Found ${models.length} models.`, type: 'success' });
      } else {
        setConnectionStatus('failed');
        addToast({ message: 'Connection successful, but no models found.', type: 'warning' });
      }
    } catch (error) {
      setConnectionStatus('failed');
      addToast({ message: 'Failed to connect to Ollama. Check the URL and ensure Ollama is running.', type: 'error' });
    }
  };

  const statusIndicator = {
    idle: null,
    testing: <Loader2 size={18} className="animate-spin text-yellow-500" />,
    success: <CheckCircle size={18} className="text-green-500" />,
    failed: <XCircle size={18} className="text-red-500" />,
  }[connectionStatus];
  
  return (
     <div className="flex flex-col gap-4">
      <h3 className="text-lg font-semibold">Ollama (Local) Configuration</h3>
      <div>
        <label className="block text-sm font-medium mb-1">Ollama Server URL</label>
        <div className="flex items-center gap-2">
            <input
              type="text"
              value={baseUrl}
              onChange={(e) => setBaseUrl(e.target.value)}
              placeholder="e.g., http://localhost:11434"
              className="w-full p-2 rounded bg-adobe-secondary border border-adobe focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {statusIndicator}
        </div>
      </div>
      <div className="flex justify-between">
        <button
          onClick={handleUseProvider}
          className="flex items-center gap-2 px-3 py-2 rounded bg-green-600 hover:bg-green-700 text-white text-sm"
        >
          {selectedProvider === 'ollama' ? '✓ Active Provider' : 'Use This Provider'}
        </button>
        <div className="flex gap-2">
          <button
             onClick={handleTestConnection}
             disabled={connectionStatus === 'testing'}
             className="flex items-center gap-2 px-3 py-2 rounded bg-gray-600 hover:bg-gray-700 text-white text-sm disabled:opacity-50"
           >
            Test Connection
           </button>
          <button
            onClick={handleSaveUrl}
            className="flex items-center gap-2 px-3 py-2 rounded bg-blue-600 hover:bg-blue-700 text-white text-sm"
          >
            <Save size={16} />
            Save URL
          </button>
        </div>
      </div>
    </div>
  );
};


/**
 * The main modal component that orchestrates provider configuration.
 */
const ProviderModal: React.FC = () => {
  const [activeProvider, setActiveProvider] = useState<ProviderID>('openai');

  const renderActivePanel = () => {
    const provider = PROVIDERS.find(p => p.id === activeProvider);
    if (!provider) return null;

    if (provider.id === 'ollama') {
      return <OllamaSettings />;
    } else {
      return <ApiKeySettings providerId={provider.id} providerName={provider.name} />;
    }
  };

  return (
    <Modal title="Provider Configuration">
      <div className="flex min-h-[300px]">
        {/* Sidebar for provider selection */}
        <nav className="w-1/3 border-r border-adobe pr-4">
          <ul className="flex flex-col gap-1">
            {PROVIDERS.map((provider) => (
              <li key={provider.id}>
                <button
                  onClick={() => setActiveProvider(provider.id)}
                  className={`w-full flex items-center gap-3 text-left p-2 rounded text-sm ${
                    activeProvider === provider.id
                      ? 'bg-blue-600 text-white'
                      : 'hover:bg-adobe-secondary'
                  }`}
                >
                  {provider.icon}
                  {provider.name}
                </button>
              </li>
            ))}
          </ul>
        </nav>
        {/* Content panel for the active provider */}
        <section className="w-2/3 pl-4">
            {renderActivePanel()}
        </section>
      </div>
    </Modal>
  );
};

export default ProviderModal;