import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ProviderID, ProviderConfig, AdobeTheme, Model } from '../types';
import { getSecureCredential, setSecureCredential } from '../utils/cep';
import { getProviderApi } from '../services/providerFactory';
import { useToastStore } from './toastStore';
import { CONFIG } from '../config';
import { logger } from '../utils/logger';

interface SettingsState {
  providers: Record<ProviderID, ProviderConfig>;
  selectedProvider: ProviderID;
  selectedModel: string;
  theme: 'light' | 'dark' | 'auto';
  adobeTheme: AdobeTheme | null;
  modelCache: Map<ProviderID, { models: Model[]; timestamp: number }>;
  setProviderApiKey: (providerId: ProviderID, apiKey: string) => void;
  setOllamaBaseUrl: (baseUrl: string) => void;
  setSelectedProvider: (providerId: ProviderID) => void;
  setSelectedModel: (modelId: string) => void;
  setTheme: (theme: 'light' | 'dark' | 'auto', adobeTheme?: AdobeTheme) => void;
  applyTheme: () => void;
  refreshProviderModels: (providerId: ProviderID) => Promise<void>;
}

const initialProviders: Record<ProviderID, ProviderConfig> = {
  openai: { models: [] },
  anthropic: { models: [] },
  google: { models: [] },
  groq: { models: [] },
  deepseek: { models: [] },
  openrouter: { models: [] },
  ollama: { baseURL: 'http://localhost:11434', models: [] },
};

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      providers: initialProviders,
      selectedProvider: 'openai',
      selectedModel: '',
      theme: 'auto',
      adobeTheme: null,
      modelCache: new Map(),

      setProviderApiKey: (providerId, apiKey) => {
        setSecureCredential(providerId, apiKey);
        // Automatically refresh models after setting a new key.
        get().refreshProviderModels(providerId);
      },

      setOllamaBaseUrl: (baseUrl: string) => {
        set((state) => ({
          providers: {
            ...state.providers,
            ollama: { ...state.providers.ollama, baseURL: baseUrl },
          },
        }));
      },

      setSelectedProvider: (providerId) => {
        set({ selectedProvider: providerId, selectedModel: '' });
        // Always refresh models when switching providers to ensure fresh data
        get().refreshProviderModels(providerId);
      },

      setSelectedModel: (modelId) => set({ selectedModel: modelId }),

      setTheme: (theme, adobeTheme) => set({ theme, ...(adobeTheme && { adobeTheme }) }),

      applyTheme: () => {
        const { theme, adobeTheme } = get();
        const root = document.documentElement;
        if ((theme === 'auto' && adobeTheme) || theme !== 'auto') {
            const isDark = theme === 'dark' || (adobeTheme && parseInt(adobeTheme.backgroundColor.substring(1), 16) < CONFIG.THEME_BRIGHTNESS_THRESHOLD);
            const themeColors = isDark ? CONFIG.DARK_THEME : CONFIG.LIGHT_THEME;

            root.style.setProperty('--adobe-bg-color', themeColors.BG_COLOR);
            root.style.setProperty('--adobe-text-color', themeColors.TEXT_COLOR);
            root.style.setProperty('--adobe-secondary-bg-color', themeColors.SECONDARY_BG_COLOR);
            root.style.setProperty('--adobe-border-color', themeColors.BORDER_COLOR);
            root.style.setProperty('--adobe-scrollbar-thumb-color', themeColors.SCROLLBAR_THUMB_COLOR);
            root.style.setProperty('--adobe-scrollbar-track-color', themeColors.SCROLLBAR_TRACK_COLOR);
        }
      },

      refreshProviderModels: async (providerId) => {
        const { modelCache } = get();
        const cached = modelCache.get(providerId);

        // Use cache if it's not older than the configured duration
        if (cached && (Date.now() - cached.timestamp) < CONFIG.MODEL_CACHE_DURATION_MS) {
          logger.log(`Using cached models for ${providerId}`);
          set((state) => ({
            providers: {
              ...state.providers,
              [providerId]: { ...state.providers[providerId], models: cached.models }
            }
          }));
          return;
        }

        try {
          logger.log(`Fetching fresh models for ${providerId}`);
          const api = getProviderApi(providerId);
          const models: Model[] = await api.getModels();

          // Update state and cache
          set((state) => ({
            providers: {
              ...state.providers,
              [providerId]: { ...state.providers[providerId], models },
            },
            modelCache: modelCache.set(providerId, { models, timestamp: Date.now() }),
          }));

          // Auto-select the first model if none is selected
          if (!get().selectedModel && models.length > 0) {
            get().setSelectedModel(models[0].id);
          }
        } catch (error: any) {
          logger.error(`Failed to fetch models for ${providerId}:`, error);
          useToastStore.getState().addToast({
            message: `Could not fetch models for ${providerId}. Check API key and connection.`,
            type: 'error',
          });
          // Clear stale models on error
           set((state) => ({
            providers: {
              ...state.providers,
              [providerId]: { ...state.providers[providerId], models: [] },
            },
          }));
        }
      },
    }),
    {
      name: CONFIG.STORAGE_KEYS.SETTINGS,
      partialize: (state) => ({
        selectedProvider: state.selectedProvider,
        selectedModel: state.selectedModel,
        theme: state.theme,
        providers: { ollama: state.providers.ollama }, // Persist Ollama URL
      }),
    }
  )
);

// Initialize API keys from secure storage - called after store creation
const initializeApiKeys = () => {
  Object.keys(initialProviders).forEach(id => {
    const key = getSecureCredential(id as ProviderID);
    if (key) {
      // You might trigger a state update or just let services use getSecureCredential directly
      logger.log(`API key loaded for ${id}`);
    }
  });
};

// Call initialization after a brief delay to ensure all modules are loaded
setTimeout(initializeApiKeys, 0);
